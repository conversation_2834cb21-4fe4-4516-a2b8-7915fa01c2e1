const formulaEngineRouter = [
  {
    path: "/formulaEngine/index",
    name: "formulaEngineIndex",
    component: () => import("@/views/formulaEngine/index.vue"),
    meta: {
      title: "公式管理",
      icon: "el-icon-data-analysis",
      keepAlive: true
    }
  },
  {
    path: "/formulaEngine/add",
    name: "formulaEngineAdd",
    component: () => import("@/views/formulaEngine/edit.vue"),
    meta: {
      title: "新增公式",
      icon: "el-icon-plus",
      keepAlive: false
    }
  },
  {
    path: "/formulaEngine/edit/:id",
    name: "formulaEngineEdit",
    component: () => import("@/views/formulaEngine/edit.vue"),
    meta: {
      title: "编辑公式",
      icon: "el-icon-edit",
      keepAlive: false
    }
  },
  {
    path: "/formulaEngine/view/:id",
    name: "formulaEngineView",
    component: () => import("@/views/formulaEngine/edit.vue"),
    meta: {
      title: "查看公式",
      icon: "el-icon-view",
      keepAlive: false
    }
  },
  {
    path: "/formulaEngine/config/:id?",
    name: "formulaEngineConfig",
    component: () => import("@/views/formulaEngine/config.vue"),
    meta: {
      title: "公式配置",
      icon: "el-icon-edit",
      keepAlive: false
    }
  },
  {
    path: "/formulaEngine/test/:id?",
    name: "formulaEngineTest",
    component: () => import("@/views/formulaEngine/test_demo.vue"),
    meta: {
      title: "公式测试",
      icon: "el-icon-cpu",
      keepAlive: false
    }
  }
];

export default formulaEngineRouter;

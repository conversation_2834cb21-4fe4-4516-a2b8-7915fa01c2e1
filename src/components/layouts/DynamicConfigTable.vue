<template>
  <div class="dynamic-config-table">
    <!-- 表格标题和操作区域 -->
    <div class="table-header">
      <div class="header-left">
        <div class="title-section">
          <i v-if="icon" :class="icon" class="title-icon"></i>
          <span class="title-text">{{ title }}</span>
          <span v-if="subtitle" class="subtitle-text">{{ subtitle }}</span>
        </div>
      </div>
      <div class="header-right" v-if="!readonly">
        <!-- 自定义操作按钮插槽 -->
        <slot name="header-actions" :data="tableData" :columns="currentColumns">
          <!-- 默认操作按钮 -->
          <el-button
            v-if="showColumnManager"
            size="mini"
            @click="openColumnManager"
            class="action-btn column-btn"
            :title="columnManagerText"
          >
            <i class="el-icon-s-grid"></i>
            {{ columnManagerText }}
          </el-button>

          <el-button
            v-if="showAddRow"
            size="mini"
            @click="addRow"
            class="action-btn add-btn"
            :title="addRowText"
          >
            <i class="el-icon-plus"></i>
            {{ addRowText }}
          </el-button>

          <el-button
            v-if="showImportExport"
            size="mini"
            @click="handleExport"
            class="action-btn export-btn"
            title="导出数据"
          >
            <i class="el-icon-download"></i>
            导出
          </el-button>

          <el-button
            v-if="showImportExport"
            size="mini"
            @click="handleImport"
            class="action-btn import-btn"
            title="导入数据"
          >
            <i class="el-icon-upload2"></i>
            导入
          </el-button>
        </slot>
      </div>
    </div>

    <!-- 数据统计信息 -->
    <div v-if="showStats" class="stats-bar">
      <span class="stats-item">
        <i class="el-icon-s-data"></i>
        共 {{ tableData.length }} 条记录
      </span>
      <span class="stats-item">
        <i class="el-icon-s-grid"></i>
        {{ currentColumns.length }} 个字段
      </span>
      <slot name="stats" :data="tableData" :columns="currentColumns"></slot>
    </div>

    <!-- 动态表格 -->
    <el-table
      ref="dynamicTable"
      :data="tableData"
      :stripe="stripe"
      :border="border"
      :size="tableSize"
      :height="height"
      :max-height="maxHeight"
      class="dynamic-table"
      :class="{
        'readonly-mode': readonly,
        'compact-mode': compact,
        [tableClass]: tableClass
      }"
      :empty-text="emptyText"
      :row-key="rowKey"
      :default-sort="defaultSort"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
      @row-dblclick="handleRowDblClick"
    >
      <!-- 选择列 -->
      <el-table-column
        v-if="showSelection"
        type="selection"
        width="55"
        align="center"
        :selectable="selectable"
      />

      <!-- 序号列 -->
      <el-table-column
        v-if="showIndex"
        type="index"
        :label="indexLabel"
        width="60"
        align="center"
        :index="indexMethod"
      />

      <!-- 动态数据列 -->
      <el-table-column
        v-for="column in currentColumns"
        :key="column.key"
        :prop="column.key"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth || 120"
        :align="column.align || 'center'"
        :sortable="column.sortable"
        :show-overflow-tooltip="column.showTooltip !== false"
        :fixed="column.fixed"
      >
        <template slot-scope="scope">
          <!-- 自定义列内容插槽 -->
          <slot
            :name="`column-${column.key}`"
            :row="scope.row"
            :column="column"
            :index="scope.$index"
            :value="scope.row[column.key]"
          >
            <!-- 编辑模式 -->
            <template v-if="!readonly">
              <!-- 输入框 -->
              <el-input
                v-if="column.type === 'input' || !column.type"
                v-model="scope.row[column.key]"
                :placeholder="getPlaceholder(column)"
                :disabled="getDisabled(column, scope.row)"
                :maxlength="column.maxlength"
                :show-word-limit="column.showWordLimit"
                size="mini"
                @change="handleCellChange(scope.row, column, scope.$index)"
                @blur="handleCellBlur(scope.row, column, scope.$index)"
              />

              <!-- 下拉选择 -->
              <el-select
                v-else-if="column.type === 'select'"
                v-model="scope.row[column.key]"
                :placeholder="getPlaceholder(column)"
                :disabled="getDisabled(column, scope.row)"
                :multiple="column.multiple"
                :filterable="column.filterable"
                :clearable="column.clearable !== false"
                size="mini"
                style="width: 100%;"
                @change="handleCellChange(scope.row, column, scope.$index)"
              >
                <el-option
                  v-for="option in getOptions(column, scope.row)"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                  :disabled="option.disabled"
                />
              </el-select>

              <!-- 数字输入 -->
              <el-input-number
                v-else-if="column.type === 'number'"
                v-model="scope.row[column.key]"
                :placeholder="getPlaceholder(column)"
                :disabled="getDisabled(column, scope.row)"
                :min="column.min"
                :max="column.max"
                :precision="column.precision"
                :step="column.step || 1"
                :controls="column.controls !== false"
                size="mini"
                style="width: 100%;"
                @change="handleCellChange(scope.row, column, scope.$index)"
              />

              <!-- 文本域 -->
              <el-input
                v-else-if="column.type === 'textarea'"
                v-model="scope.row[column.key]"
                type="textarea"
                :placeholder="getPlaceholder(column)"
                :disabled="getDisabled(column, scope.row)"
                :rows="column.rows || 2"
                :maxlength="column.maxlength"
                :show-word-limit="column.showWordLimit"
                size="mini"
                @change="handleCellChange(scope.row, column, scope.$index)"
              />

              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="column.type === 'date'"
                v-model="scope.row[column.key]"
                :type="column.dateType || 'date'"
                :placeholder="getPlaceholder(column)"
                :disabled="getDisabled(column, scope.row)"
                :format="column.format"
                :value-format="column.valueFormat"
                size="mini"
                style="width: 100%;"
                @change="handleCellChange(scope.row, column, scope.$index)"
              />

              <!-- 开关 -->
              <el-switch
                v-else-if="column.type === 'switch'"
                v-model="scope.row[column.key]"
                :disabled="getDisabled(column, scope.row)"
                :active-text="column.activeText"
                :inactive-text="column.inactiveText"
                :active-value="column.activeValue !== undefined ? column.activeValue : true"
                :inactive-value="column.inactiveValue !== undefined ? column.inactiveValue : false"
                @change="handleCellChange(scope.row, column, scope.$index)"
              />

              <!-- 自定义编辑器插槽 -->
              <slot
                v-else-if="column.type === 'custom'"
                :name="`edit-${column.key}`"
                :row="scope.row"
                :column="column"
                :index="scope.$index"
                :value="scope.row[column.key]"
                :onChange="(value) => handleCellChange(scope.row, column, scope.$index, value)"
              >
                <span class="custom-placeholder">自定义编辑器</span>
              </slot>
            </template>

            <!-- 查看模式 -->
            <template v-else>
              <div class="cell-view-content">
                {{ formatCellValue(scope.row[column.key], column, scope.row) }}
              </div>
            </template>
          </slot>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column
        v-if="shouldShowRowActions"
        :label="actionsLabel"
        :width="actionsWidth"
        :fixed="actionsFixed"
        align="center"
      >
        <template slot-scope="scope">
          <!-- 自定义行操作插槽 -->
          <slot
            name="row-actions"
            :row="scope.row"
            :index="scope.$index"
            :deleteRow="() => deleteRow(scope.$index)"
            :moveUp="() => moveRow(scope.$index, 'up')"
            :moveDown="() => moveRow(scope.$index, 'down')"
          >
            <!-- 默认行操作 -->
            <div class="row-actions">
              <el-button
                v-if="showMoveActions && scope.$index > 0"
                type="text"
                icon="el-icon-arrow-up"
                size="mini"
                @click="moveRow(scope.$index, 'up')"
                title="上移"
                class="move-btn"
              />

              <el-button
                v-if="showMoveActions && scope.$index < tableData.length - 1"
                type="text"
                icon="el-icon-arrow-down"
                size="mini"
                @click="moveRow(scope.$index, 'down')"
                title="下移"
                class="move-btn"
              />

              <el-button
                v-if="showDeleteAction"
                type="text"
                icon="el-icon-delete"
                size="mini"
                @click="deleteRow(scope.$index)"
                title="删除"
                class="delete-btn"
              />
            </div>
          </slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div v-if="showPagination" class="pagination-wrapper">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :total="total"
        :layout="paginationLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 列管理对话框 -->
    <el-dialog
      :title="columnManagerTitle"
      :visible.sync="showColumnDialog"
      :width="columnDialogWidth"
      :close-on-click-modal="false"
      :append-to-body="true"
      class="column-manager-dialog"
    >
      <div class="column-manager">
        <div class="manager-header">
          <span class="manager-title">列配置管理</span>
          <el-button
            size="mini"
            type="primary"
            @click="addColumn"
            icon="el-icon-plus"
          >
            添加列
          </el-button>
        </div>

        <el-table
          :data="editableColumns"
          class="column-config-table"
          size="mini"
        >
          <el-table-column label="列标识" width="120">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.key"
                size="mini"
                placeholder="列标识"
                :disabled="scope.row.isDefault"
              />
            </template>
          </el-table-column>

          <el-table-column label="列名称" width="120">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.label"
                size="mini"
                placeholder="列名称"
              />
            </template>
          </el-table-column>

          <el-table-column label="字段类型" width="110">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.type"
                size="mini"
                style="width: 100%;"
              >
                <el-option
                  v-for="type in fieldTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="宽度" width="80">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.width"
                size="mini"
                :min="80"
                :max="500"
                controls-position="right"
              />
            </template>
          </el-table-column>

          <el-table-column label="必填" width="60" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.required"
                size="mini"
              />
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="!scope.row.isDefault"
                type="text"
                icon="el-icon-delete"
                size="mini"
                @click="deleteColumn(scope.$index)"
                style="color: #f56c6c;"
                title="删除列"
              />
              <el-button
                v-if="!scope.row.isDefault"
                type="text"
                icon="el-icon-setting"
                size="mini"
                @click="configColumn(scope.row, scope.$index)"
                title="高级配置"
              />
              <span v-if="scope.row.isDefault" class="default-tag">默认</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelColumnEdit" size="small">取消</el-button>
        <el-button type="primary" @click="saveColumnConfig" size="small">确定</el-button>
      </div>
    </el-dialog>

    <!-- 列高级配置对话框 -->
    <el-dialog
      title="列高级配置"
      :visible.sync="showColumnConfigDialog"
      width="500px"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <div v-if="currentEditColumn" class="column-config-form">
        <!-- 这里可以添加更详细的列配置选项 -->
        <el-form :model="currentEditColumn" label-width="100px" size="small">
          <el-form-item label="占位符">
            <el-input v-model="currentEditColumn.placeholder" />
          </el-form-item>
          <el-form-item label="最大长度" v-if="['input', 'textarea'].includes(currentEditColumn.type)">
            <el-input-number v-model="currentEditColumn.maxlength" :min="1" />
          </el-form-item>
          <el-form-item label="选项配置" v-if="currentEditColumn.type === 'select'">
            <div class="options-config">
              <div v-for="(option, index) in currentEditColumn.options" :key="index" class="option-item">
                <el-input v-model="option.label" placeholder="显示文本" style="width: 120px;" />
                <el-input v-model="option.value" placeholder="值" style="width: 120px; margin-left: 8px;" />
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="removeOption(index)"
                  style="color: #f56c6c; margin-left: 8px;"
                />
              </div>
              <el-button size="mini" @click="addOption" icon="el-icon-plus">添加选项</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showColumnConfigDialog = false" size="small">取消</el-button>
        <el-button type="primary" @click="saveColumnAdvancedConfig" size="small">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
/**
 * 通用动态配置表格组件
 *
 * @description 高度可复用的动态表格组件，支持：
 * - 动态添加/删除行和列
 * - 多种字段类型（文本、选择、数字、日期等）
 * - 列管理界面
 * - 查看/编辑模式切换
 * - 完整的事件回调机制
 * - 数据验证和格式化
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
export default {
  name: 'DynamicConfigTable',

  props: {
    // 基础配置
    title: {
      type: String,
      default: '动态配置表格'
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },

    // 数据配置
    value: {
      type: Object,
      default: () => ({
        columnDefs: [],
        rowData: []
      }),
      validator: (value) => {
        return value && typeof value === 'object' &&
               Array.isArray(value.columnDefs) &&
               Array.isArray(value.rowData)
      }
    },

    // 默认列配置
    defaultColumns: {
      type: Array,
      default: () => []
    },

    // 显示控制
    readonly: {
      type: Boolean,
      default: false
    },
    compact: {
      type: Boolean,
      default: false
    },

    // 表格配置
    stripe: {
      type: Boolean,
      default: true
    },
    border: {
      type: Boolean,
      default: true
    },
    tableSize: {
      type: String,
      default: 'mini',
      validator: (value) => ['large', 'medium', 'small', 'mini'].includes(value)
    },
    height: {
      type: [String, Number],
      default: null
    },
    maxHeight: {
      type: [String, Number],
      default: null
    },
    tableClass: {
      type: String,
      default: ''
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },

    // 行配置
    rowKey: {
      type: [String, Function],
      default: null
    },
    defaultSort: {
      type: Object,
      default: null
    },

    // 选择和索引
    showSelection: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Function,
      default: null
    },
    showIndex: {
      type: Boolean,
      default: false
    },
    indexLabel: {
      type: String,
      default: '序号'
    },
    indexMethod: {
      type: Function,
      default: null
    },

    // 操作按钮配置
    showColumnManager: {
      type: Boolean,
      default: true
    },
    columnManagerText: {
      type: String,
      default: '管理列'
    },
    showAddRow: {
      type: Boolean,
      default: true
    },
    addRowText: {
      type: String,
      default: '添加行'
    },
    showImportExport: {
      type: Boolean,
      default: false
    },

    // 行操作配置
    showRowActions: {
      type: Boolean,
      default: true
    },
    actionsLabel: {
      type: String,
      default: '操作'
    },
    actionsWidth: {
      type: [String, Number],
      default: 120
    },
    actionsFixed: {
      type: [String, Boolean],
      default: false
    },
    showMoveActions: {
      type: Boolean,
      default: false
    },
    showDeleteAction: {
      type: Boolean,
      default: true
    },

    // 统计信息
    showStats: {
      type: Boolean,
      default: false
    },

    // 分页配置
    showPagination: {
      type: Boolean,
      default: false
    },
    currentPage: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 20
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },
    total: {
      type: Number,
      default: 0
    },
    paginationLayout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },

    // 列管理对话框配置
    columnManagerTitle: {
      type: String,
      default: '列管理'
    },
    columnDialogWidth: {
      type: String,
      default: '800px'
    },

    // 验证配置
    validateOnChange: {
      type: Boolean,
      default: true
    },

    // 自定义配置
    customFieldTypes: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      // 对话框状态
      showColumnDialog: false,
      showColumnConfigDialog: false,

      // 编辑状态
      editableColumns: [],
      currentEditColumn: null,
      currentEditColumnIndex: -1,

      // 内置字段类型
      builtinFieldTypes: [
        { label: '文本', value: 'input' },
        { label: '下拉选择', value: 'select' },
        { label: '数字', value: 'number' },
        { label: '文本域', value: 'textarea' },
        { label: '日期', value: 'date' },
        { label: '开关', value: 'switch' },
        { label: '自定义', value: 'custom' }
      ]
    }
  },

  computed: {
    // 当前列配置
    currentColumns() {
      return this.value.columnDefs && this.value.columnDefs.length > 0
        ? this.value.columnDefs
        : this.defaultColumns
    },

    // 表格数据
    tableData: {
      get() {
        return this.value.rowData || []
      },
      set(val) {
        this.emitChange({
          ...this.value,
          rowData: val
        })
      }
    },

    // 字段类型选项
    fieldTypes() {
      return [...this.builtinFieldTypes, ...this.customFieldTypes]
    },

    // 是否显示行操作列
    shouldShowRowActions() {
      return !this.readonly && (
        this.showRowActions ||
        this.showMoveActions ||
        this.showDeleteAction ||
        this.$scopedSlots['row-actions']
      )
    }
  },

  watch: {
    value: {
      handler() {
        this.initEditableColumns()
      },
      immediate: true,
      deep: true
    },

    defaultColumns: {
      handler() {
        this.initEditableColumns()
      },
      immediate: true
    }
  },

  methods: {
    // ==================== 数据管理 ====================

    /**
     * 触发数据变更事件
     */
    emitChange(newValue) {
      this.$emit('input', newValue)
      this.$emit('change', newValue)
    },

    /**
     * 初始化可编辑列配置
     */
    initEditableColumns() {
      this.editableColumns = (this.currentColumns || []).map(col => ({
        ...col,
        isDefault: this.defaultColumns.some(defaultCol => defaultCol.key === col.key),
        options: col.options || []
      }))
    },

    // ==================== 行操作 ====================

    /**
     * 添加行
     */
    addRow() {
      const newRow = {}
      this.currentColumns.forEach(col => {
        newRow[col.key] = this.getDefaultValue(col)
      })

      const newData = [...this.tableData, newRow]
      this.tableData = newData

      this.$emit('row-add', newRow, newData.length - 1)
      this.$emit('data-change', 'add', newRow, newData.length - 1)
    },

    /**
     * 删除行
     */
    deleteRow(index) {
      if (index < 0 || index >= this.tableData.length) return

      const row = this.tableData[index]

      this.$confirm('确定要删除这一行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const newData = [...this.tableData]
        newData.splice(index, 1)
        this.tableData = newData

        this.$emit('row-delete', row, index)
        this.$emit('data-change', 'delete', row, index)
        this.$message.success('删除成功')
      }).catch(() => {})
    },

    /**
     * 移动行
     */
    moveRow(index, direction) {
      const list = this.tableData
      if (!list || list.length <= 1) return

      let targetIndex
      if (direction === 'up' && index > 0) {
        targetIndex = index - 1
      } else if (direction === 'down' && index < list.length - 1) {
        targetIndex = index + 1
      } else {
        return
      }

      const newData = [...list]
      const temp = newData[index]
      newData[index] = newData[targetIndex]
      newData[targetIndex] = temp

      this.tableData = newData

      this.$emit('row-move', temp, index, targetIndex)
      this.$emit('data-change', 'move', temp, index, targetIndex)
    },

    // ==================== 列管理 ====================

    /**
     * 打开列管理器
     */
    openColumnManager() {
      this.initEditableColumns()
      this.showColumnDialog = true
    },

    /**
     * 添加列
     */
    addColumn() {
      this.editableColumns.push({
        key: `custom_${Date.now()}`,
        label: '新列',
        type: 'input',
        width: 120,
        required: false,
        isDefault: false,
        options: []
      })
    },

    /**
     * 删除列
     */
    deleteColumn(index) {
      if (this.editableColumns[index].isDefault) {
        this.$message.warning('默认列不能删除')
        return
      }
      this.editableColumns.splice(index, 1)
    },

    /**
     * 配置列
     */
    configColumn(column, index) {
      this.currentEditColumn = { ...column }
      this.currentEditColumnIndex = index
      this.showColumnConfigDialog = true
    },

    /**
     * 取消列编辑
     */
    cancelColumnEdit() {
      this.showColumnDialog = false
      this.initEditableColumns()
    },

    /**
     * 保存列配置
     */
    saveColumnConfig() {
      // 验证列配置
      const hasEmptyKey = this.editableColumns.some(col => !col.key || !col.label)
      if (hasEmptyKey) {
        this.$message.error('请填写完整的列标识和列名称')
        return
      }

      // 检查重复的key
      const keys = this.editableColumns.map(col => col.key)
      const uniqueKeys = [...new Set(keys)]
      if (keys.length !== uniqueKeys.length) {
        this.$message.error('列标识不能重复')
        return
      }

      // 更新列配置
      const newColumnDefs = this.editableColumns.map(col => ({
        key: col.key,
        label: col.label,
        type: col.type || 'input',
        width: col.width || 120,
        required: col.required || false,
        placeholder: col.placeholder,
        maxlength: col.maxlength,
        options: col.options || []
      }))

      // 更新行数据，确保新列有对应的字段
      const newRowData = this.tableData.map(row => {
        const newRow = { ...row }
        newColumnDefs.forEach(col => {
          if (!(col.key in newRow)) {
            newRow[col.key] = this.getDefaultValue(col)
          }
        })
        return newRow
      })

      this.emitChange({
        columnDefs: newColumnDefs,
        rowData: newRowData
      })

      this.showColumnDialog = false
      this.$emit('columns-change', newColumnDefs)
      this.$message.success('列配置保存成功')
    },

    /**
     * 保存列高级配置
     */
    saveColumnAdvancedConfig() {
      if (this.currentEditColumnIndex >= 0) {
        this.$set(this.editableColumns, this.currentEditColumnIndex, { ...this.currentEditColumn })
      }
      this.showColumnConfigDialog = false
      this.currentEditColumn = null
      this.currentEditColumnIndex = -1
    },

    /**
     * 添加选项
     */
    addOption() {
      if (!this.currentEditColumn.options) {
        this.$set(this.currentEditColumn, 'options', [])
      }
      this.currentEditColumn.options.push({
        label: '',
        value: ''
      })
    },

    /**
     * 删除选项
     */
    removeOption(index) {
      this.currentEditColumn.options.splice(index, 1)
    },

    // ==================== 单元格操作 ====================

    /**
     * 处理单元格值变更
     */
    handleCellChange(row, column, rowIndex, value) {
      const newValue = value !== undefined ? value : row[column.key]

      // 数据验证
      if (this.validateOnChange) {
        const validateResult = this.validateCellValue(newValue, column, row)
        if (!validateResult.valid) {
          this.$message.error(validateResult.message)
          return
        }
      }

      // 更新数据
      this.$set(row, column.key, newValue)

      // 触发事件
      this.$emit('cell-change', {
        row,
        column,
        rowIndex,
        value: newValue,
        oldValue: row[column.key]
      })
    },

    /**
     * 处理单元格失焦
     */
    handleCellBlur(row, column, rowIndex) {
      this.$emit('cell-blur', { row, column, rowIndex, value: row[column.key] })
    },

    /**
     * 验证单元格值
     */
    validateCellValue(value, column, row) {
      // 必填验证
      if (column.required && (value === null || value === undefined || value === '')) {
        return {
          valid: false,
          message: `${column.label}不能为空`
        }
      }

      // 类型验证
      if (column.type === 'number' && value !== '' && isNaN(Number(value))) {
        return {
          valid: false,
          message: `${column.label}必须是数字`
        }
      }

      // 长度验证
      if (column.maxlength && String(value).length > column.maxlength) {
        return {
          valid: false,
          message: `${column.label}长度不能超过${column.maxlength}个字符`
        }
      }

      // 自定义验证
      if (column.validator && typeof column.validator === 'function') {
        const result = column.validator(value, row, column)
        if (result !== true) {
          return {
            valid: false,
            message: result || `${column.label}验证失败`
          }
        }
      }

      return { valid: true }
    },

    // ==================== 工具方法 ====================

    /**
     * 获取占位符
     */
    getPlaceholder(column) {
      if (column.placeholder) return column.placeholder

      const typeMap = {
        input: '请输入',
        select: '请选择',
        number: '请输入数字',
        textarea: '请输入',
        date: '请选择日期'
      }

      const prefix = typeMap[column.type] || '请输入'
      return `${prefix}${column.label}`
    },

    /**
     * 获取禁用状态
     */
    getDisabled(column, row) {
      if (typeof column.disabled === 'function') {
        return column.disabled(row, column)
      }
      return column.disabled || false
    },

    /**
     * 获取选项列表
     */
    getOptions(column, row) {
      if (typeof column.options === 'function') {
        return column.options(row, column)
      }
      return column.options || []
    },

    /**
     * 获取默认值
     */
    getDefaultValue(column) {
      if (column.defaultValue !== undefined) {
        return typeof column.defaultValue === 'function'
          ? column.defaultValue()
          : column.defaultValue
      }

      const typeDefaults = {
        input: '',
        select: column.multiple ? [] : '',
        number: null,
        textarea: '',
        date: null,
        switch: false
      }

      return typeDefaults[column.type] || ''
    },

    /**
     * 格式化单元格值
     */
    formatCellValue(value, column, row) {
      if (value === null || value === undefined || value === '') {
        return '-'
      }

      // 选择类型的格式化
      if (column.type === 'select' && column.options) {
        const options = this.getOptions(column, row)
        if (Array.isArray(value)) {
          return value.map(v => {
            const option = options.find(opt => opt.value === v)
            return option ? option.label : v
          }).join('，')
        } else {
          const option = options.find(opt => opt.value === value)
          return option ? option.label : value
        }
      }

      // 开关类型的格式化
      if (column.type === 'switch') {
        return value ? (column.activeText || '是') : (column.inactiveText || '否')
      }

      // 日期类型的格式化
      if (column.type === 'date' && value) {
        if (column.displayFormat) {
          return this.$dayjs(value).format(column.displayFormat)
        }
        return value
      }

      // 自定义格式化
      if (column.formatter && typeof column.formatter === 'function') {
        return column.formatter(value, row, column)
      }

      return value
    },

    // ==================== 表格事件处理 ====================

    /**
     * 处理选择变更
     */
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    },

    /**
     * 处理排序变更
     */
    handleSortChange(sortInfo) {
      this.$emit('sort-change', sortInfo)
    },

    /**
     * 处理行点击
     */
    handleRowClick(row, column, event) {
      this.$emit('row-click', row, column, event)
    },

    /**
     * 处理行双击
     */
    handleRowDblClick(row, column, event) {
      this.$emit('row-dblclick', row, column, event)
    },

    // ==================== 分页事件处理 ====================

    /**
     * 处理页大小变更
     */
    handleSizeChange(size) {
      this.$emit('size-change', size)
    },

    /**
     * 处理当前页变更
     */
    handleCurrentChange(page) {
      this.$emit('current-change', page)
    },

    // ==================== 导入导出 ====================

    /**
     * 处理导出
     */
    handleExport() {
      this.$emit('export', {
        columns: this.currentColumns,
        data: this.tableData
      })
    },

    /**
     * 处理导入
     */
    handleImport() {
      this.$emit('import')
    },

    // ==================== 公共方法 ====================

    /**
     * 获取表格实例
     */
    getTableRef() {
      return this.$refs.dynamicTable
    },

    /**
     * 清空选择
     */
    clearSelection() {
      this.$refs.dynamicTable && this.$refs.dynamicTable.clearSelection()
    },

    /**
     * 切换行选择状态
     */
    toggleRowSelection(row, selected) {
      this.$refs.dynamicTable && this.$refs.dynamicTable.toggleRowSelection(row, selected)
    },

    /**
     * 设置当前行
     */
    setCurrentRow(row) {
      this.$refs.dynamicTable && this.$refs.dynamicTable.setCurrentRow(row)
    },

    /**
     * 验证所有数据
     */
    validateAll() {
      const errors = []

      this.tableData.forEach((row, rowIndex) => {
        this.currentColumns.forEach(column => {
          const validateResult = this.validateCellValue(row[column.key], column, row)
          if (!validateResult.valid) {
            errors.push({
              row: rowIndex + 1,
              column: column.label,
              message: validateResult.message
            })
          }
        })
      })

      return {
        valid: errors.length === 0,
        errors
      }
    },

    /**
     * 重置数据
     */
    resetData() {
      this.emitChange({
        columnDefs: this.defaultColumns,
        rowData: []
      })
    }
  }
}
</script>

<style lang="less" scoped>
.dynamic-config-table {
  // ==================== 表格头部 ====================
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .header-left {
      .title-section {
        display: flex;
        align-items: center;

        .title-icon {
          margin-right: 10px;
          color: #FF8030;
          font-size: 20px;
        }

        .title-text {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin-right: 12px;
        }

        .subtitle-text {
          font-size: 14px;
          color: #909399;
          font-weight: normal;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 10px;

      .action-btn {
        border-radius: 6px;
        font-size: 13px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;

        &.column-btn {
          background: #FF8030;
          border-color: #FF8030;
          color: white;

          &:hover {
            background: #e6722a;
            border-color: #e6722a;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 128, 48, 0.3);
          }
        }

        &.add-btn {
          background: #67c23a;
          border-color: #67c23a;
          color: white;

          &:hover {
            background: #5daf34;
            border-color: #5daf34;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
          }
        }

        &.export-btn {
          background: #409eff;
          border-color: #409eff;
          color: white;

          &:hover {
            background: #3a8ee6;
            border-color: #3a8ee6;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }
        }

        &.import-btn {
          background: #e6a23c;
          border-color: #e6a23c;
          color: white;

          &:hover {
            background: #cf9236;
            border-color: #cf9236;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
          }
        }
      }
    }
  }

  // ==================== 统计栏 ====================
  .stats-bar {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #fbf6ee;
    border-radius: 6px;
    border: 1px solid #cf9236;

    .stats-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #7f8c8d;

      i {
        margin-right: 6px;
        font-size: 16px;
      }
    }
  }

  // ==================== 动态表格 ====================
  .dynamic-table {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    /deep/ .el-table__header {
      background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);

      th {
        background: transparent !important;
        color: #606266;
        font-weight: 600;
        border-bottom: 2px solid #e6e6e6;
        font-size: 13px;
      }
    }

    /deep/ .el-table__body {
      tr {
        transition: background-color 0.2s ease;

        &:hover {
          background: #f8faff;
        }

        td {
          border-bottom: 1px solid #f0f0f0;
          padding: 12px 8px;

          .el-input,
          .el-select,
          .el-input-number,
          .el-date-editor {
            .el-input__inner {
              border: 1px solid #dcdfe6;
              border-radius: 4px;
              transition: border-color 0.3s ease;

              &:focus {
                border-color: #FF8030;
                box-shadow: 0 0 0 2px rgba(255, 128, 48, 0.1);
              }
            }
          }

          .el-switch {
            .el-switch__core {
              &.is-checked {
                background-color: #FF8030;
                border-color: #FF8030;
              }
            }
          }
        }
      }
    }

    // 紧凑模式
    &.compact-mode {
      /deep/ .el-table__body tr td {
        padding: 8px 6px;
      }
    }

    // 只读模式
    &.readonly-mode {
      /deep/ .el-table__body {
        tr td {
          .cell-view-content {
            padding: 8px 0;
            color: #303133;
            font-weight: 500;
            min-height: 20px;
          }
        }
      }
    }
  }

  // ==================== 行操作 ====================
  .row-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    .move-btn {
      color: #909399;
      padding: 2px 4px;

      &:hover {
        color: #409eff;
      }

      &:disabled {
        color: #c0c4cc;
        cursor: not-allowed;
      }
    }

    .delete-btn {
      color: #f56c6c;
      padding: 2px 4px;

      &:hover {
        color: #f78989;
      }
    }
  }

  // ==================== 分页器 ====================
  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;

    /deep/ .el-pagination {
      .el-pager li.active {
        background-color: #FF8030;
        border-color: #FF8030;
      }

      .btn-next,
      .btn-prev {
        &:hover {
          color: #FF8030;
        }
      }
    }
  }

  // ==================== 自定义内容 ====================
  .custom-placeholder {
    color: #c0c4cc;
    font-style: italic;
    font-size: 12px;
  }
}

// ==================== 列管理对话框 ====================
.column-manager-dialog {
  /deep/ .el-dialog__header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 24px;
    border-bottom: 1px solid #ebeef5;

    .el-dialog__title {
      color: #303133;
      font-weight: 600;
      font-size: 16px;
    }
  }

  /deep/ .el-dialog__body {
    padding: 24px;
  }

  /deep/ .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: #f8f9fa;
  }
}

.column-manager {
  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid #ebeef5;

    .manager-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .column-config-table {
    /deep/ .el-table__header {
      background: #f8f9fa;

      th {
        background: #f8f9fa !important;
        color: #606266;
        font-weight: 600;
        font-size: 13px;
      }
    }

    /deep/ .el-input,
    /deep/ .el-select,
    /deep/ .el-input-number {
      .el-input__inner {
        border: 1px solid #dcdfe6;
        border-radius: 4px;

        &:focus {
          border-color: #FF8030;
        }
      }
    }

    .default-tag {
      color: #909399;
      font-size: 12px;
      font-style: italic;
    }
  }
}

// ==================== 列高级配置 ====================
.column-config-form {
  .options-config {
    .option-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }
  }
}
</style>

<template>
  <div class="form-block">
    <el-form ref="form" :model="searchForm" :label-width="labelWidth" :inline="true" label-position="left">
      <slot></slot>
      <el-form-item v-for="(item, index) in searchFormTemp" :key="index" :label="item.label">
        <el-button v-if="item.type == 'button'" type="text" @click="handleBtn(item)">{{item.text}}</el-button>
        <el-input v-if="item.type == 'input'" @change="changeSelect(item)" :type="item.inputType||'text'" v-model="searchForm.param[item.name]"  :placeholder="item.placeholder || '请输入'"></el-input>
        <el-select v-if="item.type == 'select'" @change="changeSelect(item)" v-model="searchForm.param[item.name]" :collapse-tags="item.collapseTags"  :multiple="item.multiple"  filterable :placeholder="item.placeholder || '请选择'" :clearable="item.clearable!=undefined?item.clearable:true">
          <el-option v-for="(el, idx) in item.list" :key="idx" :label="el.dicItemName" :value="el.dicItemCode"></el-option>
        </el-select>
        <el-cascader :options="item.options" class="dt-cascader" ref="dt-cascader" v-if="item.type == 'cascader'"  v-model="searchForm.param[item.name]" @change="handleCascader(item.name, ...arguments)" filterable clearable></el-cascader>
        <el-date-picker v-if="item.type == 'datePicker'" v-model="searchForm.param[item.name]" format="yyyy-MM-dd" value-format="yyyy-MM-dd" clearable type="date" :placeholder="item.placeholder || '请选择'"></el-date-picker>
        <doubleDate v-if="item.type === 'doubleDate'" :elType="item.elType" :options="item.options" :name="item.name ? item.name : ''" :pickerOptions="item.pickerOptions||{}" @editParams="getDateTime"></doubleDate>
        <div v-if="item.type === 'doubleDate'&&item.showTitle" class="log-com-title" :style="{color:$store.state.layoutStore.themeObj.color}">默认搜索24小时内的登录日志信息</div>
      </el-form-item>
      <slot name="last"></slot>
      <el-form-item v-if="showSearch">
        <el-button type="primary" @click="normalSearch()">搜索</el-button>
        <el-button @click="normalResetQuery()" type="primary" plain :style="{color:$store.state.layoutStore.themeObj.color}">重置</el-button>
      </el-form-item>
      <slot name="join"></slot>
      <!-- 主要是为了解决回车会提交表单 -->
      <input style="display:none;">
    </el-form>
  </div>
</template>

<script>
import doubleDate from "@/components/doubleDate";
export default {
  name: "",
  props: {
    searchForm: {
      type: Object,
      default: function() {
        return {};
      }
    },
    searchFormTemp: {
      type: Array,
      default: function() {
        return [];
      }
    },
    labelWidth: {
      type: String,
      default: "auto"
    },
    showSearch: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  components: {
    doubleDate
  },
  created() {
    // this.initData();
  },
  mounted() {
    this.$nextTick(() => {
      let el = document.querySelector(".el-form-item__label-wrap");
      if (el) {
        el.style.marginLeft = 0;
      }
    });
    console.log('SearchForm.vue props.searchForm:', this.searchForm);
    console.log('SearchForm.vue props.searchFormTemp:', this.searchForm.param);
    console.log('SearchForm.vue mounted searchFormTemp:', this.searchFormTemp);
  },
  methods: {
    handleBtn(item){
      this.$emit("handleBtn",item);
    },
    changeSelect(item){
      this.$emit("changeSelect",item,this.searchForm);
      this.$forceUpdate();
    },
    normalSearch() {
      this.handleSearchForm();
      this.$emit("normalSearch", this.searchForm);
    },
    normalResetQuery() {
      this.$refs.form.resetFields();
      this.$emit("normalResetQuery");
    },
    getDateTime(arr, name) {
      this.searchFormTemp.forEach(item => {
        if (item.type == "doubleDate") {
          if (item.name == name) {
            if (arr) {
              item.options[0].value = arr[0];
              item.options[1].value = arr[1];
            } else {
              item.options[0].value = "";
              item.options[1].value = "";
            }

          }
        }
      });
      this.$emit("changeDoubleDate", arr);

    },
    handleSearchForm() {
      let tempObj = {};
      for (let item of this.searchFormTemp) {
        if (item.type == "doubleDate") {
          for (let li of item.options) {
            // tempObj[li.name] = li.value;

            if (!_.isEmpty(li.value)) {
              tempObj[li.name] = li.value;
            }
          }
        }
      }
      this.searchForm.param = Object.assign({}, this.searchForm.param, tempObj);
      this.searchForm.pageNum = 1;
    },
    handleCascader(name, val) {
      const nodesObj = this.$refs["dt-cascader"][0].getCheckedNodes()[0];
      this.$refs["dt-cascader"][0].dropDownVisible = false;
      this.$emit("handleCascader", name, val, nodesObj);
    },
  }
};
</script>

<style lang="less">
//固定输入框度
.form-block {
  background: #fff; // padding-top: 20px;
  padding-left: 11px;
  line-height:36px;
  font-size:14px;
  .el-input--medium {
    width: 215px !important;
  }
  .el-input__inner,
  .el-button--medium {
    border-radius: 6px;
  }
  .el-form-item__label {
    color: #333;
  }
  .el-form-item {
    margin-bottom: 13px;
  }
  .el-date-editor .el-range-separator {
    width: initial;
  }
  .dt-cascader {
    .el-input--medium {
      width: 250px !important;
    }
  }
}
</style>

<template>
  <div id="app" class="wh100">
    <router-view />
  </div>
</template>
<script>
const version = require("element-ui/package.json").version;   // 获取elment-ui版本
const ORIGINAL_THEME = "#409EFF";                             // elment 默认主题
import { getParamString } from '@/utils/utils'
import {
  getTenantUsers
} from "@/api/index";
export default {
  data() {
    return {
        
    };
  },
  watch: {
    "$store.state.layoutStore.themeObj": {
      deep: true,
      handler(newObj, oldObj) {
        if (window.dtHandleTheme) {
          window.dtHandleTheme(newObj.color, oldObj.color, version)
        }
        window.themeObj = newObj;
        window.color = "red"
      }
    },
  },
  created() {
    this.init();
  },

  methods: {
    init() {
      let funcId = getParamString("funcId");
      let tenantId = getParamString("tenantId");
      let userName = decodeURIComponent(getParamString('userName'));
      let userId = getParamString("userId");
      let access_token = getParamString("access_token");
      if(access_token){
        sessionStorage.setItem("LoginAccessToken", access_token);
      }
      if(tenantId){
        sessionStorage.setItem("tenantId", tenantId);
      }
      if(funcId){
        sessionStorage.setItem("funcId", funcId);
      }
      this.$store.commit("layoutStore/setCurrentLoginUser", { access_token, tenantId, funcId, userName,userId });
      this.$store.commit("layoutStore/setCacheArr", { status: "clear" });
      this.updatePage(access_token, tenantId, funcId, userName)
      //获取登录用户在该应用下权限位等信息
      this.$store.dispatch('getWebUserInfo');

      //获取企客用户权限
      this.$store.dispatch('getQikeUserInfo');

      this.getTenantUsers()
    },
    handleTheme(color, themeObj) {
      if (window.dtHandleTheme) {
        window.dtHandleTheme(color, ORIGINAL_THEME, version)
      }
      this.$store.commit("layoutStore/setThemeObj", themeObj);
    },
    async getTenantUsers() {
      if(!localStorage.getItem("tenantUsers")){
        let res = await getTenantUsers()
        if (res) {
          localStorage.setItem("tenantUsers", JSON.stringify(res))
        }
      }
    },
    setGlobalStyle(themeObj){//设置全局颜色样式类
        let ele=document.createElement("style");
        ele.setAttribute("type", "text/css");
        ele.innerHTML=`
            .textColor{
                color:${themeObj.color};
            }
            .textLightColor{
                color:${themeObj.navTagUnselectedColor};
            }
            .bgColor{
                background:${themeObj.color};
            }
            .bgLightColor{
                background:${themeObj.navTagUnselectedColor};
            }
        `;
        ele.innerHTML.replace(/(<br>)/g,"");
        document.getElementsByTagName('head')[0].appendChild(ele)
    },
    updatePage(access_token, tenantId, funcId, userName,userId){
      if(access_token&&tenantId&&funcId){
        localStorage.setItem("setCurrentLoginUser", JSON.stringify({ access_token, tenantId, funcId, userName, userId}));
      }else{
        let {access_token, tenantId, funcId, userName,userId} = JSON.parse(localStorage.getItem("setCurrentLoginUser"))
        sessionStorage.setItem("LoginAccessToken",access_token);
        sessionStorage.setItem("LoginFuncId", funcId);
        sessionStorage.setItem("LoginTenantId", tenantId);
        this.$store.commit("layoutStore/setCurrentLoginUser", { access_token, tenantId, funcId, userName,userId });
      }
    }
  },
  mounted() {

    let sessionThemeObj= JSON.parse(sessionStorage.getItem("themeObj"))
    

    let themeObj = {
      color: getParamString("themeColor")||sessionThemeObj.color,
      tableBtnActiveColor: getParamString("themeColor")||sessionThemeObj.tableBtnActiveColor,
      navTagUnselectedColor: getParamString("navTagColor")||sessionThemeObj.navTagUnselectedColor,
      text: ""
    }

    if(themeObj.color){
      sessionStorage.setItem("themeObj", JSON.stringify(themeObj))
    }
    if (themeObj.color) {
        setTimeout(() => {
            this.handleTheme(themeObj.color, themeObj)
      }, 700);
    }

    window.addEventListener('message', (e) => {
      if (e.data.theme) {
        //在此处标识是父系统嵌入的子系统，需要子系统隐藏左侧菜单栏，头部的Tabs栏目，去掉主体内容的padding样式
        this.handleTheme(e.data.theme.color, e.data.theme)
      }

    })
    this.setGlobalStyle(themeObj);
  }
};
</script>


<style lang="less">
html,
head,
body {
  margin: 0;
  width: 100%;
  height: 100%;
}

.wh100 {
  width: 100%;
  height: 100%;
}

@import "./assets/style/dt-public.less";
@import "./assets/style/common.less";

@media screen and (max-width: 1440px) {
  .form-block {
    .el-form-item__content {
      .el-input--medium {
        width: 180px !important;
      }
    }
  }
}

@media screen and (min-width: 1440px) {
  .form-block {
    .el-form-item__content {
      .el-input--medium {
        width: 215px !important;
      }
    }
  }
}

@media screen and (max-width: 1280px) {
  .form-block {
    .el-form-item__content {
      .el-input--medium {
        width: 160px !important;
      }
    }
  }
}
</style>


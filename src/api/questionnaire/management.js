import http from "@/utils/httpService";
import {rootPath} from '@/utils/globalParam';

const transformRequestData = (data) => {
  console.log('API transformRequestData 输入数据:', data)

  const transformed = {
    ...data,
    title: data.name || data.title, // 将name映射为title
    // enterpriseTypes保持为数组格式，后端期望List<String>
    enterpriseTypes: Array.isArray(data.enterpriseTypes) ? data.enterpriseTypes : [],
    // 确保questions数据被正确传递
    questions: data.questions || []
  }

  console.log('API transformRequestData 输出数据:', transformed)
  return transformed
};

// 获取问卷列表（分页查询）
export const getQuestionnaireList = (params = {}) => {
  return http.Axios.post(rootPath + "/api/questionnaire/page", params)
};

// 获取问卷详情
export const getQuestionnaireDetail = (id) => {
  return http.Axios.get(rootPath + `/api/questionnaire/${id}`)
};

// 创建问卷
export const createQuestionnaire = (data) => {
  const requestData = transformRequestData(data);
  return http.Axios.post(rootPath + "/api/questionnaire", requestData);
};

// 更新问卷
export const updateQuestionnaire = (id, data) => {
  const requestData = transformRequestData(data);
  return http.Axios.put(rootPath + `/api/questionnaire/${id}`, requestData);
};

// 删除问卷
export const deleteQuestionnaire = (id) => {
  return http.Axios.delete(rootPath + `/api/questionnaire/${id}`);
};

// 提交问卷答案
export const submitQuestionnaireAnswer = (data) => {
  return http.Axios.post(rootPath + "/api/questionnaire/answer/submit", data);
};

// 获取企业的问卷答案
export const getEnterpriseAnswers = (questionnaireId, enterpriseId) => {
  return http.Axios.get(rootPath + `/api/questionnaire/${questionnaireId}/answer/${enterpriseId}`);
};

// 获取题目选项列表
export const getQuestionOptions = (questionId) => {
  return http.Axios.get(rootPath + `/api/questionnaire/${questionId}/options`);
};

import Vue from "vue";
import Vuex from "vuex";
// import createVuexAlong from "vuex-along";
import * as api from '@/api/index'
Vue.use(Vuex);

const state = {
  dicMap: {},
  userTenants: {},
  breadcrumbNav:[]
};
const getters = {
  getBreadcrumbNav: state => {
    return state.breadcrumbNav;
  },
  getLastBreadcrumb: state => {
    let lastIndex = state.breadcrumbNav.length-1
    return state.breadcrumbNav[lastIndex];
  }
};
const mutations = {
  mapDicData(state, data) {
    state.dicMap[data.dicCode] = data.dicItems;
  },
  setUserTenants(state, data){
    state.userTenants = data;
  },
  //清空面包屑
  clearBreadcrumbNav(state, data){
    state.breadcrumbNav=[];
  },
  //增加面包屑
  pushBreadcrumbNav(state, data){
    if(!data.name||!data.router){
      throw Error('setBreadcrumbNav方法传值错误！');
    }
    let item = {
      name:data.name,
      router:data.router
    }
    if(state.breadcrumbNav.findIndex(obj=>obj.name === data.name)>-1)return;
    state.breadcrumbNav.push(item)
  },
  //移除面包屑
  popBreadcrumbNav(state,num){
    if(typeof num === 'number'){
      //删除数组最后的num个item
      state.breadcrumbNav.splice(num);
    }else{
      //删除数组最后一个item
      state.breadcrumbNav.pop();
    }
  }
};

const actions = {
  getWebUserInfo({commit}) {
    //获取登录用户在该应用下的租户
		api.getWebUserInfo({}).then(data => {
      if(data){
          let authsetArr = [];
          let authsetObj = [];
          if(data.funcAuthDTO){
            data.funcAuthDTO.forEach(item => {
              authsetArr.push(item.authCode);
              authsetObj.push(item);
            });
          }
          commit("layoutStore/setAuthSet", authsetArr);
          commit("layoutStore/setAuthSetObj", authsetObj);
          commit("layoutStore/addCurrentLoginUser",{isAdmin:data.isAdmin})
          //  if(data.appTenantListVo){
          //    data.appTenantListVo.forEach(el => {
          //      el.dicItemCode = el.tenantId;
          //      el.dicItemName = el.shortName;
          //    });     
          //    commit('setUserTenants', data.appTenantListVo);
          //  }
      }
    })
  },
  getQikeUserInfo({commit}) {
    api.getQikeUserInfo({}).then(data => {
      if(data){
        commit("layoutStore/setQikeUserInfo", data);
      }
    })
  }
};

import layoutStore from "./layoutStore.js"; //公共状态
export default new Vuex.Store({
  state,
  getters,
  mutations,
  modules: {
    layoutStore
  },
  // plugins: [createVuexAlong()],
  actions
});



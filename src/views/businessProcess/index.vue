<template>
  <div class="business-process-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <i class="el-icon-s-operation title-icon"></i>
          <div class="title-text">
            <h2 class="main-title">业务流程配置</h2>
            <p class="sub-title">配置不同业务类型的流程执行顺序，启用的流程将按顺序执行</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置表格 -->
    <div class="config-table-container">
      <div class="table-card">
        <div class="table-wrapper">
          <table class="config-table">
            <thead>
              <tr>
                <th class="business-column">业务名称</th>
                <th class="process-column" v-for="process in processDefinitions" :key="process.code">
                  {{ process.name }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="business in tableData" :key="business.businessCode">
                <td class="business-name">
                  <div class="business-info">
                    <i class="el-icon-office-building business-icon"></i>
                    <span>{{ business.businessName }}</span>
                  </div>
                </td>
                <td class="process-switch" v-for="process in processDefinitions" :key="process.code">
                  <el-switch
                    :value="business.processConfigs.includes(process.code)"
                    @change="handleProcessToggle(business.businessCode, process.code, $event)"
                    :disabled="loading"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button
        type="primary"
        size="medium"
        :loading="saveLoading"
        @click="handleSave"
        icon="el-icon-check"
      >
        保存配置
      </el-button>
      <el-button
        size="medium"
        @click="handleReset"
        :disabled="loading"
        icon="el-icon-refresh"
      >
        重置
      </el-button>
    </div>
  </div>
</template>

<script>
import { getBusinessProcessList, saveBusinessProcess } from '@/api/businessProcess'

export default {
  name: 'BusinessProcessConfig',
  data() {
    return {
      loading: false,
      saveLoading: false,

      // 流程定义（固定顺序）
      processDefinitions: [
        { code: 'KYC_QUERY', name: '企业KYC查询' },
        { code: 'COMPLIANCE_APPROVAL', name: '企业合规审批' },
        { code: 'QUESTIONNAIRE_COLLECTION', name: '企业问卷收集' },
        { code: 'RISK_MANAGEMENT', name: '企业风险管理方案' }
      ],

      // 业务数据
      tableData: [
        {
          businessCode: 'ONLINE_PRODUCT',
          businessName: '线上产品',
          processConfigs: []
        },
        {
          businessCode: 'EMPLOYEE_WELFARE',
          businessName: '员福保障',
          processConfigs: []
        },
        {
          businessCode: 'COMPREHENSIVE_PROTECTION',
          businessName: '综合保障',
          processConfigs: []
        }
      ]
    }
  },

  mounted() {
    this.loadData()
  },

  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const response = await getBusinessProcessList()
        if (response && Array.isArray(response)) {
          // 直接使用后端返回的数据，更新对应的业务配置
          response.forEach(backendBusiness => {
            const business = this.tableData.find(item => item.businessCode === backendBusiness.businessCode)
            if (business) {
              business.processConfigs = backendBusiness.processConfigs || []
            }
          })
        }
      } catch (error) {
        console.error('加载业务流程配置失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },



    // 处理流程开关切换
    handleProcessToggle(businessCode, processCode, enabled) {
      const business = this.tableData.find(item => item.businessCode === businessCode)
      if (!business) return

      if (enabled) {
        // 启用：按固定顺序插入
        if (!business.processConfigs.includes(processCode)) {
          business.processConfigs.push(processCode)
          // 重新排序保证顺序
          business.processConfigs = this.sortProcessConfigs(business.processConfigs)
        }
      } else {
        // 禁用：从数组中移除
        const index = business.processConfigs.indexOf(processCode)
        if (index > -1) {
          business.processConfigs.splice(index, 1)
        }
      }
    },

    // 按固定顺序排序流程配置
    sortProcessConfigs(processConfigs) {
      const orderMap = {}
      this.processDefinitions.forEach((process, index) => {
        orderMap[process.code] = index
      })

      return processConfigs.sort((a, b) => {
        return (orderMap[a] || 999) - (orderMap[b] || 999)
      })
    },

    // 保存配置
    async handleSave() {
      this.saveLoading = true
      try {
        await saveBusinessProcess(this.tableData)
        this.$message.success('保存成功')
      } catch (error) {
        console.error('保存业务流程配置失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.saveLoading = false
      }
    },

    // 重置配置
    handleReset() {
      this.$confirm('确定要重置所有配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清空所有配置
        this.tableData.forEach(business => {
          business.processConfigs = []
        })
        this.$message.success('重置成功')
      }).catch(() => {
        // 用户取消
      })
    }
  }
}
</script>

<style lang="less" scoped>
.business-process-container {
  min-height: 100vh;
  background: white;
  overflow: hidden;

  .page-header {
    background-color: #fbf6ee;
    border-bottom: 1px solid #f7ecdd;

    .header-content {
      padding: 20px 28px;

      .title-section {
        display: flex;
        align-items: center;
        gap: 12px;

        .title-icon {
          width: 36px;
          height: 36px;
          background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          color: white;
        }

        .title-text {
          .main-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 2px 0;
            display: block;
          }

          .sub-title {
            margin: 0;
            color: #7f8c8d;
            font-size: 12px;
            font-weight: 400;
            line-height: 1.5;
          }
        }
      }
    }
  }

  .config-table-container {
    background: white;
    border-top: 1px solid #f7ecdd;

    .table-card {
      padding: 28px;

      .table-wrapper {
        overflow-x: auto;

        .config-table {
          width: 100%;
          border-collapse: collapse;
          border: 1px solid #f7ecdd;
          border-radius: 6px;

          th, td {
            padding: 16px 12px;
            text-align: center;
            border-bottom: 1px solid #f7ecdd;
            border-right: 1px solid #f7ecdd;

            &:last-child {
              border-right: none;
            }
          }

          thead {
            background: #fefdfb;

            th {
              color: #2c3e50;
              font-weight: 600;
              font-size: 14px;

              &.business-column {
                text-align: left;
                width: 200px;
              }

              &.process-column {
                width: 150px;
              }
            }
          }

          tbody {
            tr {
              transition: all 0.3s ease;

              &:hover {
                background: rgba(215, 162, 86, 0.05) !important;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(215, 162, 86, 0.1);
              }

              &:last-child {
                td {
                  border-bottom: none;
                }
              }
            }

            .business-name {
              text-align: left;

              .business-info {
                display: flex;
                align-items: center;

                .business-icon {
                  font-size: 16px;
                  color: #D7A256;
                  margin-right: 8px;
                }

                span {
                  font-size: 14px;
                  color: #2c3e50;
                  font-weight: 500;
                }
              }
            }

            .process-switch {
              padding: 16px 12px;

              /deep/ .el-switch {
                .el-switch__core {
                  border-color: #f7ecdd;
                  background-color: #f7ecdd;

                  &:after {
                    background-color: white;
                  }
                }

                &.is-checked {
                  .el-switch__core {
                    border-color: #D7A256;
                    background-color: #D7A256;
                  }
                }

                &:hover {
                  .el-switch__core {
                    border-color: #E6B366;
                  }

                  &.is-checked .el-switch__core {
                    background-color: #E6B366;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .action-section {
    padding: 20px 28px;
    border-top: 1px solid #f7ecdd;
    background: white;
    text-align: center;

    .el-button {
      margin: 0 8px;
      padding: 12px 24px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 6px;

      &.el-button--primary {
        background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);
        color: white;

        &:hover {
          background: linear-gradient(135deg, #C69146 0%, #D5A356 100%);
          box-shadow: 0 4px 12px rgba(215, 162, 86, 0.4);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }

      &.el-button--default {
        color: #7f8c8d;
        border-color: rgba(127, 140, 141, 0.3);
        background: rgba(127, 140, 141, 0.1);

        &:hover {
          background: #7f8c8d;
          color: white;
          border-color: #7f8c8d;
        }
      }
    }
  }
}
</style>

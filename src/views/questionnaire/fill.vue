<template>
  <InfoPageLayout
    title="问卷填写"
    subtitle="请认真填写以下问卷，我们将根据您的答案生成风险评估报告"
    icon="el-icon-edit-outline"
    :breadcrumb-items="breadcrumbItems"
    @back="$router.back()"
    v-loading.fullscreen.lock="loading"
    element-loading-text="问卷提交中..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(255, 255, 255, 0.9)"
  >
    <!-- 问卷信息卡片 -->
    <InfoCard
      :title="form.title"
      :description="form.description"
      icon="el-icon-document"
      class="questionnaire-info-card"
    >
      <template #extra>
        <div class="questionnaire-meta">
          <InfoItem label="题目数量" :value="form.questions.length + '题'" />
          <InfoItem label="预计用时" :value="estimatedTime" />
        </div>
      </template>
    </InfoCard>

    <!-- 问卷题目卡片 -->
    <InfoCard title="问卷题目" icon="el-icon-edit" class="questions-card">
       <div class="questions-list">
         <div v-for="(q, idx) in form.questions" :key="q.id" class="question-item">
           <div class="question-header">
             <div class="question-number">{{ idx + 1 }}</div>
             <div class="question-info">
               <h4 class="question-title">{{ q.title }}</h4>
               <div class="question-meta">
                 <el-tag
                   :type="getQuestionTypeTag(q.type).type"
                   size="small"
                   class="type-tag"
                 >
                   {{ getQuestionTypeTag(q.type).text }}
                 </el-tag>
                 <el-tag
                   v-if="q.scoreItem"
                   type="success"
                   size="small"
                   class="score-tag"
                 >
                   {{ q.scoreItem }}
                 </el-tag>
               </div>
             </div>
           </div>

           <div class="question-content">
             <!-- 调试信息 -->
             <div v-if="q.options && q.options.length === 0" class="debug-info" style="color: red; margin-bottom: 10px;">
               ⚠️ 该题目没有选项数据
             </div>
             <div v-if="!q.options" class="debug-info" style="color: red; margin-bottom: 10px;">
               ⚠️ 该题目的options字段为空
             </div>

             <!-- 单选题 -->
             <el-radio-group
               v-if="q.type === 'single'"
               v-model="answers[q.id]"
               class="option-group"
             >
               <div
                 v-for="option in q.options"
                 :key="option.id"
                 class="option-item"
               >
                 <el-radio :label="option.id" class="option-radio">
                   <span class="option-text">{{ option.text }}</span>
                   <span class="option-score">{{ option.score }}分</span>
                 </el-radio>
               </div>
             </el-radio-group>

             <!-- 多选题 -->
             <el-checkbox-group
               v-else-if="q.type === 'multi'"
               v-model="answers[q.id]"
               class="option-group"
             >
               <div
                 v-for="option in q.options"
                 :key="option.id"
                 class="option-item"
               >
                 <el-checkbox :label="option.id" class="option-checkbox">
                   <span class="option-text">{{ option.text }}</span>
                   <span class="option-score">{{ option.score }}分</span>
                 </el-checkbox>
               </div>
             </el-checkbox-group>

             <!-- 简答题 -->
             <el-input
               v-else-if="q.type === 'text'"
               v-model="answers[q.id]"
               type="textarea"
               :rows="4"
               placeholder="请详细描述您的想法..."
               class="text-input"
               maxlength="500"
               show-word-limit
             />
           </div>
         </div>
       </div>

       <div class="submit-section">
         <el-button
           type="primary"
           size="large"
           @click="handleSubmit"
           class="submit-btn"
           :loading="loading"
         >
           <i class="el-icon-check"></i>
           提交问卷并生成报告
         </el-button>
         <p class="submit-tip">提交后将自动生成您的风险评估报告</p>
       </div>
     </InfoCard>
   </InfoPageLayout>
</template>

<script>
import { getQuestionnaireDetail, submitQuestionnaireAnswer } from '@/api/questionnaire/management'
import { getConstantList } from '@/api/constantSetting'
import { getRiskMatrixList, getScoreItemList, getRiskMatrixDetail } from '@/api/riskMatrix'
import { calculateFormula } from '@/api/formulaEngine'
import InfoPageLayout from '@/components/layouts/InfoPageLayout.vue'
import InfoCard from '@/components/layouts/InfoCard.vue'
import InfoItem from '@/components/layouts/InfoItem.vue'

export default {
  name: 'QuestionnaireFill',
  components: {
    InfoPageLayout,
    InfoCard,
    InfoItem
  },
  data() {
    return {
      form: {
        id: '',
        title: '',
        description: '',
        questions: []
      },
      answers: {},
      loading: false,
      // 计算过程数据
      calculationProcess: {
        questionnaireAnswers: [],
        scoreItemResults: [],
        basicParameters: {},
        riskMatrix: null,
        categoryResults: [],
        finalReport: {}
      }
    }
  },
  mounted() {
    this.loadQuestionnaireData()
  },
  computed: {
    estimatedTime() {
      const questionCount = this.form.questions.length
      const estimatedMinutes = Math.ceil(questionCount * 0.5) // 每题约30秒
      return `${estimatedMinutes}分钟`
    },
    breadcrumbItems() {
      return [
        { text: '首页', to: '/', icon: 'el-icon-house' },
        { text: '问卷管理', to: '/questionnaire', icon: 'el-icon-document' },
        { text: '问卷填写', icon: 'el-icon-edit-outline' }
      ]
    }
  },
  methods: {
    async loadQuestionnaireData() {
      this.loading = true
      try {
        const questionnaireId = this.$route.params.id
        if (!questionnaireId) {
          this.$message.error('问卷ID不能为空')
          this.$router.back()
          return
        }

        console.log('加载问卷详情，ID:', questionnaireId)
        const response = await getQuestionnaireDetail(questionnaireId)
        console.log('问卷详情响应:', response)

        if (response) {
          console.log('原始后端响应数据:', response)

          // 如果后端没有返回问题数据，使用测试数据
          let questions = response.questions || []
          if (questions.length === 0) {
            console.log('后端没有返回问题数据，使用测试数据')
            questions = this.getTestQuestions()
          }

          // 数据格式适配
          this.form = {
            id: response.id,
            title: response.title,
            description: response.description,
            enterpriseTypes: response.enterpriseTypeList || [],
            questions: this.transformQuestions(questions)
          }

          console.log('转换后的问卷数据:', this.form)
          console.log('问题数量:', this.form.questions.length)

          // 打印每个问题的选项信息
          this.form.questions.forEach((q, index) => {
            console.log(`问题${index + 1} (ID: ${q.id}):`, q.title)
            console.log(`  类型: ${q.type}`)
            console.log(`  选项数量: ${q.options ? q.options.length : 0}`)
            if (q.options && q.options.length > 0) {
              console.log(`  选项详情:`, q.options)
            }
          })

          // 初始化答案对象
          this.initDefaultAnswers()
        } else {
          this.$message.error('加载问卷数据失败')
        }
      } catch (error) {
        console.error('加载问卷数据失败:', error)
        this.$message.error('加载问卷数据失败')
      } finally {
        this.loading = false
      }
    },

    // 转换后端问题数据格式为前端期望格式
    transformQuestions(backendQuestions) {
      return backendQuestions.map(q => ({
        id: q.id,
        title: q.title,
        type: q.type,
        scoreItem: q.scoreItem,
        options: this.transformOptions(q.options || []), // 转换选项格式
        required: q.required === 1
      }))
    },

    // 转换选项数据格式
    transformOptions(backendOptions) {
      console.log('转换选项数据，原始数据:', backendOptions)

      const transformedOptions = backendOptions.map((option, index) => {
        const transformed = {
          id: option.id || option.label || index, // 兼容多种ID格式
          text: option.optionText || option.text || '', // 兼容两种字段名
          score: option.score || 0,
          value: option.optionValue || option.value || option.label,
          label: option.label // 保留原始label
        }

        console.log(`选项${index}转换:`, option, '->', transformed)
        return transformed
      })

      console.log('转换后的选项数据:', transformedOptions)
      return transformedOptions
    },

    // 获取测试问题数据（临时用于调试）
    getTestQuestions() {
      return [
        {
          id: 1,
          title: "您觉得贵企业管理层在重视风险管理这件事上，达到了什么程度呢？",
          type: "single",
          scoreItem: "管理层承诺与文化导向",
          required: 1,
          options: [
            { id: 1, optionText: "临时关注，只看重短期运营风险，没长远打算。", score: 1 },
            { id: 2, optionText: "刚开始认识到重要性，但参与得少，投入的资源也不多。", score: 2 },
            { id: 3, optionText: "明确支持风险管控，参与部分决策，给予一些资源。", score: 3 },
            { id: 4, optionText: "深度参与决策，按风险情况分配资源，能有针对性地完成对应风险应对。", score: 4 },
            { id: 5, optionText: "走在行业前列，能引领风险管理地方向，提前预知风险。", score: 5 }
          ]
        },
        {
          id: 2,
          title: "企业里风险责任划分清楚吗，相关人员参与度如何？",
          type: "single",
          scoreItem: "风险责任明确",
          required: 1,
          options: [
            { id: 6, optionText: "业务流程乱，责任不明确，相关人员基本不参与。", score: 1 },
            { id: 7, optionText: "初步建立了流程，相关人员开始参与，但责任界定还很模糊。", score: 2 },
            { id: 8, optionText: "明确了流程和责任，有基本监督，相关人员参与识别、评估和应对。", score: 3 },
            { id: 9, optionText: "流程和责任体系很完善，相关专业人员深度参与。", score: 4 },
            { id: 10, optionText: "流程和责任体系在行业标准，样样都能对标行业标杆。", score: 5 }
          ]
        }
      ]
    },

    initDefaultAnswers() {
      // 初始化答案对象
      this.form.questions.forEach(q => {
        if (q.type === 'single') {
          this.$set(this.answers, q.id, null)
        } else if (q.type === 'multi') {
          this.$set(this.answers, q.id, [])
        } else {
          this.$set(this.answers, q.id, '')
        }
      })
    },

    getQuestionTypeTag(type) {
      const typeMap = {
        'single': { type: 'primary', text: '单选题' },
        'multi': { type: 'warning', text: '多选题' },
        'text': { type: 'info', text: '简答题' }
      }
      return typeMap[type] || { type: 'default', text: '未知' }
    },

    async handleSubmit() {
      // 验证问卷数据
      if (!this.form.id) {
        this.$message.error('问卷数据异常，请刷新页面重试')
        return
      }

      // 验证必填题目
      const unanswered = this.form.questions.filter(q => {
        // 只验证必填题目
        if (!q.required) return false

        if (q.type === 'single') {
          return !this.answers[q.id]
        } else if (q.type === 'multi') {
          return !this.answers[q.id] || this.answers[q.id].length === 0
        } else {
          return !this.answers[q.id] || this.answers[q.id].trim() === ''
        }
      })

      if (unanswered.length > 0) {
        this.$message.warning(`还有 ${unanswered.length} 道必填题目未完成`)
        return
      }

      this.loading = true
      try {
        // 1. 先提交问卷答案到后端
        await this.submitAnswersToBackend()

        // 2. 开始完整的评分计算流程
        await this.performCompleteCalculation()

        this.$message.success('问卷提交成功！正在生成风险矩阵...')
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 提交答案到后端
    async submitAnswersToBackend() {
      try {
        // 构造答案数据
        const answers = []

        this.form.questions.forEach(q => {
          const answer = {
            questionId: q.id,
            answerContent: '',
            score: 0
          }

          if (q.type === 'single') {
            const selectedOptionId = this.answers[q.id]
            if (selectedOptionId) {
              const selectedOption = q.options.find(opt => opt.id === selectedOptionId)
              if (selectedOption) {
                answer.answerContent = selectedOptionId.toString()
                answer.score = selectedOption.score || 0
              }
            }
          } else if (q.type === 'multi') {
            const selectedOptionIds = this.answers[q.id] || []
            if (selectedOptionIds.length > 0) {
              answer.answerContent = selectedOptionIds.join(',')
              answer.score = selectedOptionIds.reduce((total, optionId) => {
                const option = q.options.find(opt => opt.id === optionId)
                return total + (option ? option.score || 0 : 0)
              }, 0)
            }
          } else if (q.type === 'text') {
            answer.answerContent = this.answers[q.id] || ''
            answer.score = 0 // 简答题暂不计分
          }

          answers.push(answer)
        })

        // 提交数据结构
        const submitData = {
          questionnaireId: this.form.id,
          enterpriseId: this.getCurrentEnterpriseId(), // 需要获取当前企业ID
          answers: answers
        }

        console.log('提交答案数据:', submitData)
        const response = await submitQuestionnaireAnswer(submitData)
        console.log('答案提交响应:', response)

        if (response) {
          console.log('答案提交成功')
        } else {
          throw new Error('答案提交失败')
        }
      } catch (error) {
        console.error('提交答案到后端失败:', error)
        throw error
      }
    },

    // 获取当前企业ID（临时方法，实际应从用户信息或路由参数获取）
    getCurrentEnterpriseId() {
      // 这里需要根据实际业务逻辑获取企业ID
      // 可能从用户登录信息、路由参数或其他地方获取
      return this.$route.params.enterpriseId || 1 // 临时使用默认值
    },

    async performCompleteCalculation() {
      // 从问卷数据或路由参数获取企业类型
      const enterpriseType = this.$route.params.enterpriseType ||
                           (this.form.enterpriseTypes && this.form.enterpriseTypes[0]) ||
                           'CDE'

      // 1. 收集问卷答案
      this.calculationProcess.questionnaireAnswers = this.collectQuestionnaireAnswers()

      // 2. 获取基础参数配置
      await this.loadBasicParameters()

      // 3. 获取评分项列表
      await this.loadScoreItems()

      // 4. 计算评分项分数
      await this.calculateScoreItems()

      // 5. 获取风险矩阵配置
      await this.loadRiskMatrix(enterpriseType)

      // 6. 计算核心类别得分和档次
      await this.calculateCategories()

      // 7. 生成最终报告
      this.generateFinalReport()

      // 8. 跳转到报告页面
      this.navigateToReport()
    },

    collectQuestionnaireAnswers() {
      const answers = []
      this.form.questions.forEach(q => {
        const answer = {
          questionId: q.id,
          scoreItem: q.scoreItem,
          question: q.question,
          selectedOptions: [],
          totalScore: 0
        }

        if (q.type === 'single') {
          const selectedOption = q.options.find(opt => opt.id === this.answers[q.id])
          if (selectedOption) {
            answer.selectedOptions = [selectedOption]
            answer.totalScore = selectedOption.score
          }
        } else if (q.type === 'multi') {
          this.answers[q.id].forEach(optionId => {
            const selectedOption = q.options.find(opt => opt.id === optionId)
            if (selectedOption) {
              answer.selectedOptions.push(selectedOption)
              answer.totalScore += selectedOption.score
            }
          })
        }

        answers.push(answer)
      })

      return answers
    },

    async loadBasicParameters() {
      try {
        const response = await getConstantList({ pageSize: 100 })
        if (response.code === 200) {
          // 将参数转换为键值对
          this.calculationProcess.basicParameters = {}
          response.data.list.forEach(param => {
            this.calculationProcess.basicParameters[param.name] = param.defaultValue
          })
        }
      } catch (error) {
        console.error('加载常数配置失败:', error)
      }
    },

    async loadScoreItems() {
      try {
        const response = await getScoreItemList({ pageSize: 100 })
        if (response.code === 200) {
          this.calculationProcess.scoreItems = response.data.list
        }
      } catch (error) {
        console.error('加载评分项失败:', error)
      }
    },

    async calculateScoreItems() {
      this.calculationProcess.scoreItemResults = []
      console.log('开始计算评分项分数，问卷答案数量:', this.calculationProcess.questionnaireAnswers.length)
      console.log('评分项数量:', this.calculationProcess.scoreItems ? this.calculationProcess.scoreItems.length : 0)

      for (const answer of this.calculationProcess.questionnaireAnswers) {
        console.log('处理问卷答案，评分项:', answer.scoreItem, '总分:', answer.totalScore)

        const scoreItem = this.calculationProcess.scoreItems.find(item =>
          item.name === answer.scoreItem
        )

        if (scoreItem) {
          console.log('找到匹配的评分项:', scoreItem.name, 'ID:', scoreItem.id, '公式ID:', scoreItem.formulaId)

          try {
            // 准备公式变量
            const variables = {
              X: answer.totalScore, // 问卷答案分数作为主变量
              ...this.calculationProcess.basicParameters // 其他基础参数
            }

            console.log('公式变量:', variables)

            // 调用公式计算API
            const formulaResponse = await calculateFormula(scoreItem.formulaId, variables)

            if (formulaResponse.code === 200) {
              const calculatedScore = parseFloat(formulaResponse.data.result)
              const finalScore = calculatedScore * scoreItem.coefficient

              console.log('公式计算结果:', calculatedScore, '系数:', scoreItem.coefficient, '最终分数:', finalScore)

              const result = {
                scoreItemId: scoreItem.id,
                scoreItemName: scoreItem.name,
                formulaId: scoreItem.formulaId,
                formulaName: scoreItem.formulaName,
                coefficient: scoreItem.coefficient,
                questionnaireScore: answer.totalScore,
                calculatedScore: calculatedScore,
                finalScore: finalScore,
                variables: variables,
                calculationDetails: formulaResponse.data
              }

              this.calculationProcess.scoreItemResults.push(result)
            } else {
              console.error('公式计算失败:', formulaResponse.message)
            }
          } catch (error) {
            console.error(`计算评分项 ${answer.scoreItem} 失败:`, error)
          }
        } else {
          console.warn(`未找到匹配的评分项: ${answer.scoreItem}`)
        }
      }

      console.log('评分项计算完成，结果数量:', this.calculationProcess.scoreItemResults.length)
    },

    async loadRiskMatrix(enterpriseType) {
      try {
        const response = await getRiskMatrixList({ pageSize: 100 })
        if (response.code === 200) {
          console.log('获取到风险矩阵列表:', response.data.list)
          console.log('查找企业类型:', enterpriseType)

          // 找到匹配企业类型的风险矩阵
          this.calculationProcess.riskMatrix = response.data.list.find(matrix => {
            console.log('当前矩阵企业类型:', matrix.enterpriseTypes)
            return matrix.enterpriseTypes && matrix.enterpriseTypes.includes(enterpriseType)
          })

          if (!this.calculationProcess.riskMatrix) {
            console.error('未找到匹配的风险矩阵，尝试默认使用第一个企业风险矩阵')
            // 如果没有找到匹配的，尝试使用企业风险管理矩阵
          this.calculationProcess.riskMatrix = response.data.list.find(matrix =>
              matrix.name === '企业风险管理矩阵' || matrix.code === 'ENTERPRISE_RISK_MATRIX'
            )

            if (!this.calculationProcess.riskMatrix) {
              console.error('未找到企业风险管理矩阵，使用第一个可用矩阵')
              // 如果还是没找到，使用第一个矩阵
              this.calculationProcess.riskMatrix = response.data.list[0]
            }

            if (!this.calculationProcess.riskMatrix) {
              this.$message.warning(`未找到适用于企业类型 ${enterpriseType} 的风险矩阵配置`)
            } else {
              console.log('使用默认风险矩阵:', this.calculationProcess.riskMatrix.name)
            }
          } else {
            console.log('找到匹配的风险矩阵:', this.calculationProcess.riskMatrix.name)
          }

          // 获取风险矩阵详情，确保包含categories
          if (this.calculationProcess.riskMatrix) {
            const detailResponse = await getRiskMatrixDetail(this.calculationProcess.riskMatrix.id)
            if (detailResponse.code === 200) {
              this.calculationProcess.riskMatrix = detailResponse.data
              console.log('风险矩阵详情加载成功，包含类别数:',
                this.calculationProcess.riskMatrix.categories ?
                this.calculationProcess.riskMatrix.categories.length : 0
              )
            }
          }
        }
      } catch (error) {
        console.error('加载风险矩阵失败:', error)
      }
    },

    async calculateCategories() {
      if (!this.calculationProcess.riskMatrix) {
        console.error('未找到匹配的风险矩阵')
        return
      }

      if (!this.calculationProcess.riskMatrix.categories) {
        console.error('风险矩阵缺少categories数据')
        return
      }

      console.log('开始计算类别评分，类别数量:', this.calculationProcess.riskMatrix.categories.length)
      console.log('评分项结果数量:', this.calculationProcess.scoreItemResults.length)

      this.calculationProcess.categoryResults = []

      for (const category of this.calculationProcess.riskMatrix.categories || []) {
        console.log('处理类别:', category.name)

        // 确保category.scoreItems存在
        if (!category.scoreItems || !Array.isArray(category.scoreItems)) {
          console.warn(`类别 ${category.name} 没有关联的评分项`)
          continue
        }

        console.log(`类别 ${category.name} 关联的评分项ID:`, category.scoreItems)

        // 找到该类别关联的评分项结果
        const categoryScoreItems = this.calculationProcess.scoreItemResults.filter(result => {
          const isMatch = category.scoreItems.includes(result.scoreItemId)
          if (isMatch) {
            console.log(`找到匹配的评分项: ${result.scoreItemName}, ID: ${result.scoreItemId}`)
          }
          return isMatch
        })

        console.log(`类别 ${category.name} 匹配到的评分项数量:`, categoryScoreItems.length)

        if (categoryScoreItems.length > 0) {
          let categoryScore = 0

          if (category.calculationMethod === 'sum') {
            categoryScore = categoryScoreItems.reduce((sum, item) => sum + item.finalScore, 0)
            // 确保分数不超过100分
            categoryScore = Math.min(categoryScore, 100)
            console.log(`类别 ${category.name} 使用sum计算方法，总分:`, categoryScore, categoryScore > 100 ? '(已限制为100分)' : '')
          } else if (category.calculationMethod === 'average') {
            categoryScore = categoryScoreItems.reduce((sum, item) => sum + item.finalScore, 0) / categoryScoreItems.length
            // 确保分数不超过100分
            categoryScore = Math.min(categoryScore, 100)
            console.log(`类别 ${category.name} 使用average计算方法，平均分:`, categoryScore, categoryScore > 100 ? '(已限制为100分)' : '')
          } else {
            // 默认使用sum
            categoryScore = categoryScoreItems.reduce((sum, item) => sum + item.finalScore, 0)
            // 确保分数不超过100分
            categoryScore = Math.min(categoryScore, 100)
            console.log(`类别 ${category.name} 使用默认sum计算方法，总分:`, categoryScore, categoryScore > 100 ? '(已限制为100分)' : '')
          }

          // 确保levels存在
          if (!category.levels || !Array.isArray(category.levels)) {
            console.warn(`类别 ${category.name} 没有定义评分等级，使用默认等级`)
            category.levels = [
              { name: '低', minValue: 0, maxValue: 33, description: '低级别风险' },
              { name: '中', minValue: 34, maxValue: 66, description: '中级别风险' },
              { name: '高', minValue: 67, maxValue: 100, description: '高级别风险' }
            ]
          }

          // 确定档次
          const level = this.determineCategoryLevel(categoryScore, category.levels)
          console.log(`类别 ${category.name} 得分 ${categoryScore}，对应等级:`, level.name)

          const categoryResult = {
            categoryId: category.id,
            categoryName: category.name,
            scoreItems: categoryScoreItems,
            calculationMethod: category.calculationMethod,
            categoryScore: categoryScore,
            level: level,
            levels: category.levels
          }

          this.calculationProcess.categoryResults.push(categoryResult)
        } else {
          console.warn(`类别 ${category.name} 没有找到匹配的评分项结果`)
        }
      }

      console.log('类别评分计算完成，结果数量:', this.calculationProcess.categoryResults.length)
    },

    determineCategoryLevel(score, levels) {
      // 确保levels是一个数组
      if (!Array.isArray(levels) || levels.length === 0) {
        return { name: '未知', minValue: 0, maxValue: 100, description: '未定义等级' }
      }

      // 格式化分数，保留两位小数
      const formattedScore = parseFloat(score.toFixed(2));
      console.log(`格式化后的分数: ${formattedScore}`);

      // 尝试找到匹配的等级
      for (const level of levels) {
        if (formattedScore >= level.minValue && formattedScore <= level.maxValue) {
          console.log(`找到匹配的等级: ${level.name}, 范围: ${level.minValue}-${level.maxValue}`);
          return level;
        }
      }

      // 如果没有匹配的等级，返回最后一个等级
      console.log(`未找到匹配的等级，使用最后一个等级: ${levels[levels.length - 1].name}`);
      return levels[levels.length - 1];
    },

    generateFinalReport() {
      let totalScore = this.calculationProcess.scoreItemResults.reduce((sum, item) => sum + item.finalScore, 0);
      // 总分不限制100分，因为这是所有评分项的总和

      let averageScore = this.calculationProcess.scoreItemResults.length > 0 ?
        totalScore / this.calculationProcess.scoreItemResults.length : 0;
      // 确保平均分不超过100分
      averageScore = Math.min(averageScore, 100);

      // 格式化分数，保留两位小数
      const formattedTotalScore = parseFloat(totalScore.toFixed(2));
      const formattedAverageScore = parseFloat(averageScore.toFixed(2));

      console.log(`总分: ${formattedTotalScore}, 平均分: ${formattedAverageScore}`);

      // 计算整体风险等级
      let overallRiskLevel = '低风险';
      if (formattedAverageScore < 20) {
        overallRiskLevel = '一档 (极低风险)';
      } else if (formattedAverageScore < 40) {
        overallRiskLevel = '二档 (低风险)';
      } else if (formattedAverageScore < 60) {
        overallRiskLevel = '三档 (中风险)';
      } else if (formattedAverageScore < 80) {
        overallRiskLevel = '四档 (高风险)';
      } else {
        overallRiskLevel = '五档 (极高风险)';
      }

      this.calculationProcess.finalReport = {
        enterpriseType: this.$route.params.enterpriseType ||
                       (this.form.enterpriseTypes && this.form.enterpriseTypes[0]) ||
                       'CDE',
        questionnaireId: this.form.id,
        questionnaireTitle: this.form.title,
        totalScore: formattedTotalScore,
        averageScore: formattedAverageScore,
        overallRiskLevel: overallRiskLevel,
        categoryResults: this.calculationProcess.categoryResults,
        calculationProcess: this.calculationProcess,
        createTime: new Date().toLocaleString()
      }
    },

    navigateToReport() {
      // 确保有最终报告数据才跳转
      if (!this.calculationProcess.finalReport || !this.calculationProcess.categoryResults || this.calculationProcess.categoryResults.length === 0) {
        console.error('没有足够的报告数据，无法跳转到报告页面')
        this.$message.error('生成报告失败，请重试')
        return
      }

      // 跳转到风险矩阵详情页
      this.$router.push({
        name: 'riskMatrixDetail',
        params: {
          reportData: this.calculationProcess.finalReport
        }
      })
    }
  }
}
</script>

<style scoped>
.questionnaire-info-card {
  margin-bottom: 24px;
}

.questionnaire-meta {
  display: flex;
  gap: 24px;
  align-items: center;
}

.questions-card {
  margin-bottom: 24px;
}

.questions-list {
  margin-bottom: 32px;
}

.question-item {
  margin-bottom: 32px;
  padding: 24px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.question-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.question-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 16px;
}

.question-number {
  background: #667eea;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.question-info {
  flex: 1;
}

.question-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.question-meta {
  display: flex;
  gap: 8px;
  align-items: center;
}

.type-tag {
  font-size: 12px;
}

.score-tag {
  font-size: 12px;
}

.question-content {
  margin-left: 48px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  overflow: visible;
  min-height: 60px;
  display: flex;
  align-items: center;
}

.option-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.option-radio,
.option-checkbox {
  width: 100%;
  padding: 16px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.option-radio /deep/ .el-radio__label,
.option-checkbox /deep/ .el-checkbox__label {
  width: 100%;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding-left: 8px !important;
  line-height: 1.4 !important;
  white-space: normal !important;
}

.option-radio /deep/ .el-radio__input,
.option-checkbox /deep/ .el-checkbox__input {
  margin-right: 8px;
  flex-shrink: 0;
}

.option-text {
  color: #2c3e50;
  font-size: 15px;
  flex: 1;
  margin-right: 12px;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.option-score {
  color: #667eea;
  font-weight: 600;
  font-size: 14px;
  background: #f0f4ff;
  padding: 6px 10px;
  border-radius: 6px;
  margin-left: 12px;
  flex-shrink: 0;
  min-width: 50px;
  text-align: center;
  border: 1px solid #e1e8ff;
}

.text-input {
  margin-top: 8px;
}

.text-input /deep/ .el-textarea__inner {
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  font-size: 15px;
  line-height: 1.6;
}

.text-input /deep/ .el-textarea__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.submit-section {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #e2e8f0;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 16px 40px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 200px;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submit-tip {
  margin-top: 12px;
  color: #7f8c8d;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .questionnaire-meta {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .question-item {
    padding: 16px;
  }

  .question-header {
    gap: 12px;
  }

  .question-content {
    margin-left: 0;
    margin-top: 16px;
  }

  .option-radio,
  .option-checkbox {
    padding: 12px;
  }

  .option-radio /deep/ .el-radio__label,
  .option-checkbox /deep/ .el-checkbox__label {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 8px !important;
  }

  .option-text {
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .option-score {
    margin-left: 0 !important;
    align-self: flex-end;
  }

  .submit-btn {
    width: 100%;
    min-width: auto;
  }
}

/* 额外的样式修复，确保分数显示正确 */
.option-radio .el-radio__label,
.option-checkbox .el-checkbox__label {
  position: relative !important;
  overflow: visible !important;
  text-overflow: initial !important;
  white-space: normal !important;
}

/* 确保分数标签不被截断 */
.option-score {
  position: relative !important;
  z-index: 1 !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 修复可能的Element UI样式冲突 */
.el-radio__label,
.el-checkbox__label {
  max-width: none !important;
}
</style>

<template>
  <div class="process-list">
    <div>
      <div class="nav-list">
        <div v-for="(item, index) in navBarlist" :key="index" :class="{ li: true, active: currentIndex == index }"
          :style="{
            color: currentIndex == index ? themeObj.color : '',
            'border-color': currentIndex == index ? themeObj.color : '',
          }" @click="navChange(item, index)">
          {{ item.name }}
        </div>
      </div>
    </div>

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>
    <SearchForm v-if="currentIndex == '1'" :searchForm="initParam" :searchFormTemp="searchFormTemp"
      @normalSearch="normalSearch" @normalResetQuery="normalResetQuery"></SearchForm>
    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover>
      <el-table-column align="center" prop="id" label="流程ID"></el-table-column>
      <el-table-column align="center" prop="processName" label="流程名称"></el-table-column>
      <el-table-column align="center" prop="processDesc" label="流程说明"></el-table-column>
      <el-table-column align="center" prop="updateTime" width="200" label="上次编辑时间"></el-table-column>
      <el-table-column align="center" label="创建人" width="100">
        <template slot-scope="scope">{{ scope.row.createId | getNickName(scope.row.createId) }}</template>
      </el-table-column>
      <el-table-column align="center" prop="createTime" width="200" label="创建时间"></el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="processDetail(scope.row, 'view')">查看</el-button>
          <el-button class="btn-center" type="text" @click="processDetail(scope.row, 'edit')">编辑</el-button>
          <el-button class="btn-center" type="text" v-if="currentIndex == 2" @click="handleCopyProcessDefine(scope.row)">复制</el-button>
          <el-button class="btn-center" type="text" v-if="scope.row.status == 0 || scope.row.status == 2"
            @click="handleStartProcessDefine(scope.row)">启用</el-button>
          <el-button class="btn-center" type="text" v-if="scope.row.status == 1"
            @click="handleStopProcessDefine(scope.row)">禁用</el-button>
          <el-button class="btn-center" type="text" v-if="currentIndex == 2" @click="del(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
      :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>


    <DtPopup :isShow.sync="showDelPopup" @close="showDelPopup = false" title="提示" center :footer="false" width="30%">
      <div class="check-popup">
        <div style="text-align: center">{{ confirmMessage }}</div>
        <div class="btn-wrap">
          <el-button type="primary" class="btn-width"
            :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px' }"
            @click.stop="showDelPopup = false">取消</el-button>
          <el-button type="primary" class="btn-width"
            :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color }"
            @click.stop="confirmHandle">确认</el-button>
        </div>
      </div>
    </DtPopup>


  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import { getDicItemList } from "@/config/tool.js";
import { getProcessDefineList, copyProcessDefine, deleteProcessDefine, startProcessDefine, stopProcessDefine } from "@/api/processManagement";

export default {
  name: "processList",
  data() {
    return {
      toolListProps: {
        toolTitle: "流程管理",
        toolList: []
      },
      tableData: [{}],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          createTimeEnd: "",
          createTimeStart: "",
          processName: "",
          createName: "",
          processStatus: "",
          processType: ""
        }
      },
      searchFormTemp: [
        {
          label: "流程名称",
          name: "processName",
          type: "input",
          width: "200px"
        },
        {
          label: "创建人",
          name: "createName",
          type: "input",
          width: "200px"
        },
        {
          name: "createTime",
          type: "doubleDate",
          label: "创建时间",
          placeholder: "请选择",
          elType: "DateTimePicker",
          options: [
            {
              name: "createTimeStart",
              placeholder: "请输入开始时间",
              value: "",
            },
            {
              name: "createTimeEnd",
              placeholder: "请输入结束时间",
              value: "",
            },
          ],
          fixedShow: true,
          tempShow: true,
        },
        {
          label: "状态",
          name: "processStatus",
          type: "select",
          list: []
        }
      ],
      total: 0,
      showDelPopup: false,
      showPopup: false,
      currentItem: "",
      title: "",
      currentIndex: 0,
      confirmType: "",
      confirmMessage: "",
      currentNav: {
        name: "默认机会流程",
        type: "1"
      },
      navBarlist: [
        {
          name: "默认机会流程",
          type: "1"
        },
        {
          name: "自定义机会流程",
          type: "2"
        }
      ]
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  async created() {
    await this.getDicFun();
    // 初始化时设置工具列表
    this.toolListProps.toolList = [];
    this.initData();
  },
  activated() {
    // 监听从 processInput 页面回退时重新初始化数据
    this.initData();
  },
  methods: {
    async getDicFun() {
      let res = await getDicItemList("elms.process.status");
      if (res) {
        this.searchFormTemp[3].list = res;
      }
    },
    navChange(item, index) {
      this.currentNav = item;
      this.currentIndex = index
      if (index == 0) {
        this.initParam = _.cloneDeep(this.$options.data().initParam);
        this.searchFormTemp[2].options[0].value = "";
        this.searchFormTemp[2].options[1].value = "";
      }
      // 根据当前标签页设置工具列表
      if (index === 1) {
        // 自定义机会流程
        this.toolListProps.toolList = [
          {
            name: "新增自定义流程",
            icon: "",
            // btnCode: "mes:exam:quickReply"
          }
        ];
      } else {
        // 默认机会流程
        this.toolListProps.toolList = [];
      }
      this.initData();
    },
    async initData() {
      this.initParam.param.processType = this.currentNav.type;
      let res = await getProcessDefineList(this.initParam);
      if (res) {
        this.total = res.total;
        this.tableData = [];
        if (res.list) {
          this.tableData = res.list ? res.list : [{}];
        }
      }
    },
    handleTool(item) {
      if (item.name == "新增自定义流程") {
        this.$router.push({
          name: "processInput",
          query: {
            processType: "2",
            mode: "add"  // add-新增, edit-编辑, view-查看
          }
        })
      }
    },


    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.searchFormTemp[2].options[0].value = "";
      this.searchFormTemp[2].options[1].value = "";
      this.initData();
    },
    async handleCopyProcessDefine(row) {
      let res = await copyProcessDefine({ id: row.id });
      if (res) {
        this.processDetail(res, 'copy')
      }
    },
    handleStartProcessDefine(row) {
      this.currentItem = row;
      this.confirmType = 'start';
      this.confirmMessage = '请确认是否启用？';
      this.showDelPopup = true;
    },
    handleStopProcessDefine(row) {
      this.currentItem = row;
      this.confirmType = 'stop';
      this.confirmMessage = '请确认是否禁用？';
      this.showDelPopup = true;
    },
    async handleStartProcessDefineConfirm() {
      let res = await startProcessDefine({ id: this.currentItem.id });
      if (res) {
        this.initData();
      }
      this.showDelPopup = false;
    },
    async handleStopProcessDefineConfirm() {
      let res = await stopProcessDefine({ id: this.currentItem.id });
      if (res) {
        this.initData();
      }
      this.showDelPopup = false;
    },
    processDetail(row, mode) {
      this.$router.push({
        name: "processInput",
        query: {
          processType: this.currentNav.type,
          id: row.id,
          mode: mode  // add-新增, edit-编辑, view-查看, copy-复制
        }
      })
    },
    del(row) {
      this.currentItem = row;
      this.confirmType = 'delete';
      this.confirmMessage = '请确认是否删除？';
      this.showDelPopup = true;
    },
    async delHandle() {
      let res = await deleteProcessDefine({ id: this.currentItem.id });
      if (res) {
        this.initData();
      }
      this.showDelPopup = false;
    },
    confirmHandle() {
      switch (this.confirmType) {
        case 'delete':
          this.delHandle();
          break;
        case 'start':
          this.handleStartProcessDefineConfirm();
          break;
        case 'stop':
          this.handleStopProcessDefineConfirm();
          break;
      }
    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    }
  }
};
</script>
<style lang="less">
.process-list {
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;

    .li {
      width: 128px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 16px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }
}



.check-popup {
  width: 100%;

  .btn-wrap {
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: center;

    .btn-width {
      width: 100px;
    }
  }

  .end-exam-text {
    margin-top: 10px;
  }
}
</style>
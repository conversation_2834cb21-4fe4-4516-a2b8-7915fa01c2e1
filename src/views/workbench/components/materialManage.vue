<template>
  <div class="member-manage">
    <!-- 生态服务 -->
    <TableToolTemp :toolListProps="serviceMenuListPros" class="log-tool"></TableToolTemp>
    <el-form ref="detailForm" :model="detailForm" label-width="150px" label-position="left">
      <el-form-item label="是否附加生态服务" prop="divisionId">
        <el-radio-group v-model="detailForm.hasService">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <el-table :data="serviceProductList" class="dt-table" style="width: 100%" v-hover row-key="id">
      <el-table-column align="center" prop="companyCode" label="服务类型" width="250">
        <template slot-scope="scope">
          <span class="communication-item">{{ scope.row.serviceType }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="policyNo" label="服务产品" width="250">
        <template slot-scope="scope">
          <div>
            {{ scope.row.productName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200">
        <template slot-scope="scope">
          <div class="action-buttons" v-if="!(scope.row.isDefault == 1 && scope.row.joinType == 1)">
            <el-button class="btn-center" type="text" @click="opendProductPopup()">
              选择产品
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 投标情况 -->
    <TableToolTemp :toolListProps="bideMenuListPros" class="log-tool"></TableToolTemp>
    <el-form ref="detailForm" :model="detailForm" label-width="150px" label-position="left">
      <el-form-item label="是否投标" prop="divisionId">
        <el-radio-group v-model="detailForm.isBid">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="投标时间" prop="divisionId">
        <el-date-picker type="datetimerange" placeholder="请选择沟通日期年/月/日" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="投标结果" prop="divisionId">
        <el-radio-group v-model="detailForm.bidResult">
          <el-radio :label="1">成功</el-radio>
          <el-radio :label="0">失败</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <!-- 经纪委托授权书 -->
    <TableToolTemp :toolListProps="authMenuListPros" class="log-tool"></TableToolTemp>

    <!-- 询价记录 -->
    <TableToolTemp :toolListProps="inquiryMenuListPros" class="log-tool"></TableToolTemp>

    <!-- 排分结果 -->
    <TableToolTemp :toolListProps="resultMenuListPros" class="log-tool"></TableToolTemp>

    <!-- 选择产品弹窗 -->
    <DtPopup :isShow.sync="showProductPopup" @close="showProductPopup = false" title="选择生态服务产品" center :footer="true"
      width="1200px">
      <div class="productContainer">
        <!-- 选择产品 -->
        <div class="product-left">
          <el-table :data="healthProductList" class="dt-table" style="width: 100%" v-hover row-key="id"
            @current-change="getProductInfo">
            <el-table-column width="55" fixed="left">
              <template slot-scope="scope">
                <el-radio class="radio" :label="scope.row" v-model="selHealthProduct">{{ "" }}</el-radio>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="productName" label="健康服务" width="250">
              <template slot-scope="scope">
                <span class="communication-item">{{ scope.row.productName }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-table :data="rescueProductList" class="dt-table" style="width: 100%" v-hover row-key="id"
            @current-change="getProductInfo">
            <el-table-column width="55" fixed="left">
              <template slot-scope="scope">
                <el-radio class="radio" :label="scope.row" v-model="selRescueProduct">{{ "" }}</el-radio>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="productName" label="救援服务" width="250">
              <template slot-scope="scope">
                <span class="communication-item">{{ scope.row.productName }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 产品详情渲染 -->
        <div class="product-right">
          <div class="productInfo">
            <div class="title"><span>{{ productInfo.productName }}</span></div>
            <div class="sub_title"><span>购买条件</span></div>
            <div class="sub_title">
              <span>有效期：{{ productInfo.servicePeriod }}{{ productInfo.servicePeriodUnit }}</span>
              <span>年龄范围：{{ productInfo.minAge }}-{{ productInfo.maxAge }}周岁</span>
              <span>生效日期：T+{{ productInfo.startDay }}-T+{{ productInfo.endDay }}</span>
            </div>
            <div class="content">
              <table>
                <thead>
                  <tr>
                    <th v-for="(item, index) in processedTableData.headers" :key="index" 
                        :style="{ width: processedTableData.columnWidths[index] }">{{ item }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(subArray, subIndex) in processedTableData.dataList" :key="subIndex">
                    <td v-for="(item, index) in subArray" :key="index">
                      <!-- 内容列的特殊处理 -->
                      <template v-if="index === 1 && Array.isArray(item)">
                        <div class="content-items">
                          <span v-for="(contentItem, contentIndex) in item" :key="contentIndex" class="content-item">
                            <a v-if="contentItem && contentItem.includes('#_#')" :style="{ color: themeObj.color }"
                              @click="getServiceInfo(contentItem)">{{ contentItem.split("#_#")[0] }}</a>
                            <span v-else>{{ contentItem || '' }}</span>
                          </span>
                        </div>
                      </template>
                      <!-- 其他列的正常处理 -->
                      <template v-else>
                        <a v-if="item && item.includes('#_#')" :style="{ color: themeObj.color }"
                          @click="getServiceInfo(item)">{{ item.split("#_#")[0] }}</a>
                        <span v-else>{{ item || '' }}</span>
                      </template>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </DtPopup>

    <!-- 服务详情弹窗 -->
    <DtPopup :isShow.sync="showServiceItemPopup" @close="showServiceItemPopup = false" :title="serviceItemInfo.itemTitle"
      center :footer="false">
      <div class="serviceContainer">
        <div v-if="serviceItemInfo.serviceContent" class="serviceItem">
          <div v-html="serviceItemInfo.serviceContent"></div>
        </div>
        <div v-if="serviceItemInfo.validity" class="serviceItem">
          <div class="title">服务时效：</div>
          <span>{{ serviceItemInfo.validity }}</span>
        </div>
        <div v-if="serviceItemInfo.waitingPeriod" class="serviceItem">
          <div class="title">等待期：</div>
          <span>{{ serviceItemInfo.waitingPeriod }}</span>
        </div>
        <div v-if="serviceItemInfo.serviceTarget" class="serviceItem">
          <div class="title">服务对象：</div>
          <span>{{ serviceItemInfo.serviceTarget }}</span>
        </div>
        <div v-if="serviceItemInfo.serviceTimes" class="serviceItem">
          <div class="title">服务次数：</div>
          <span>{{ serviceItemInfo.serviceTimes }}</span>
        </div>
      </div>
    </DtPopup>
  </div>
</template>

<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import { esptProductInfo, esptProductItemInfo, esptProductList, updateOrder } from "../../../api/workbench";
import { getDicItemList } from "../../../config/tool";



export default {
  name: "materialManage",
  inject: ['opportunityDetailFn', 'opportunityFn', 'enterpriseFn', 'opportunityProcessLogFn'],
  data() {
    return {
      opportunityId: this.$route.query.id,

      detailForm: {
        hasService: 1,
        isBid: 1,
        bidResult: 1
      },

      /*** 生态服务 ***/
      serviceMenuListPros: {
        toolTitle: "生态服务"
      },
      // 服务产品列表
      serviceProductList: [
        { serviceType: "健康服务", productName: "健康服务优享版" },
        { serviceType: "救援服务", productName: "境内救援服务" }
      ],
      showProductPopup: false,
      showServiceItemPopup: false,
      // 健康服务产品列表
      healthProductList: [],
      // 救援服务产品列表
      rescueProductList: [],
      selHealthProduct: {},
      selRescueProduct: {},
      // 产品详情
      productInfo: {
        productCode: "",
        productName: "",
        servicePeriod: null,
        servicePeriodUnit: "",
        minAge: null,
        maxAge: null,
        startDay: null,
        endDay: null,
        headers: [],
        dataList: []
      },
      // 服务详情
      serviceItemInfo: {
        itemTitle: "",
        serviceContent: "",
        serviceCode: "",
        validity: "",
        serviceTarget: "",
        serviceTimes: "",
        waitingPeriod: ""
      },
      // 救援类型数组
      rescueServiceItemType: [],

      /*** 投标情况 ***/
      bideMenuListPros: {
        toolTitle: "投标情况"
      },

      /*** 经纪委托授权书 ***/
      authMenuListPros: {
        toolTitle: "经纪委托授权书"
      },

      /*** 询价记录 ***/
      inquiryMenuListPros: {
        toolTitle: "询价记录"
      },

      /*** 排分结果 ***/
      resultMenuListPros: {
        toolTitle: "排分结果"
      },

    }
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  filters: {

  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    // 从父组件获取注入的数据
    injectedOpportunityDetail() {
      return this.opportunityDetailFn ? this.opportunityDetailFn() : null;
    },
    injectedOpportunity() {
      return this.opportunityFn ? this.opportunityFn() : null;
    },
    injectedEnterprise() {
      return this.enterpriseFn ? this.enterpriseFn() : null;
    },
    injectedOpportunityProcessLog() {
      return this.opportunityProcessLogFn ? this.opportunityProcessLogFn() : null;
    },
         // 处理表格数据，确保头部铺满，数据行以最长的行为模板填充
     processedTableData() {
       if (!this.productInfo.headers || !this.productInfo.dataList) {
         return {
           headers: [],
           dataList: [],
           columnWidths: []
         };
       }

       const headers = this.productInfo.headers;
       const dataList = this.productInfo.dataList;
       
       // 检查是否有"内容"列
       const contentColumnIndex = headers.findIndex(header => header === '内容');
       
       if (contentColumnIndex !== -1) {
         // 有内容列的情况：重新组织数据结构
         const processedHeaders = [];
         const processedDataList = [];
         
         // 第一列：服务项目/计划
         processedHeaders.push(headers[0]);
         
         // 第二列：内容（合并所有后续列）
         processedHeaders.push('内容');
         
         // 处理数据行
         dataList.forEach(row => {
           const processedRow = [];
           
           // 第一列：服务项目/计划
           processedRow.push(row[0] || '');
           
           // 第二列：内容（合并所有后续数据）
           const contentItems = row.slice(1).filter(item => item && item.trim() !== '');
           processedRow.push(contentItems);
           
           processedDataList.push(processedRow);
         });
         
         // 设置列宽：第一列固定，第二列铺满剩余空间
         const columnWidths = ['140px', '1fr'];
         
         return {
           headers: processedHeaders,
           dataList: processedDataList,
           columnWidths: columnWidths
         };
       } else {
         // 没有内容列的情况：多列表格，需要合理分配列宽
         const maxLength = Math.max(...dataList.map(row => row.length), headers.length);
         
         const processedHeaders = [...headers];
         while (processedHeaders.length < maxLength) {
           processedHeaders.push('');
         }
         
         const processedDataList = dataList.map(row => {
           const processedRow = [...row];
           while (processedRow.length < maxLength) {
             processedRow.push('');
           }
           return processedRow;
         });
         
         // 为多列表格设置合理的列宽
         const columnWidths = processedHeaders.map((header, index) => {
           if (index === 0) {
             // 第一列（服务项目/计划）设置较宽
             return '160px';
           } else {
             // 其他列设置较窄，确保所有列都能显示
             return '70px';
           }
         });
         
         return {
           headers: processedHeaders,
           dataList: processedDataList,
           columnWidths: columnWidths
         };
       }
     }
  },
  async created() {
    this.initData();
  },
  methods: {

    async initData() {
      this.getEsptProductList();
      this.getRescueServiceItemType();
    },



    /*** 生态服务 ***/
    async getRescueServiceItemType() {
      const response = await getDicItemList("espt.rescueServiceItemType");
      if (response) {
        this.rescueServiceItemType = response;
      }
    },
    getServiceInfo(item) {
      // console.log(item);
      if (item.includes("#_#")) {
        let itemName = item.split("#_#")[0];
        let itemId = item.split("#_#")[1];
        if (itemId) {
          this.getEsptItemInfo(itemId, itemName);
        }
      }
    },
    async getEsptItemInfo(itemId, itemName) {
      let param = { serviceItemId: itemId };
      const res = await esptProductItemInfo(param);
      if (res) {
        this.serviceItemInfo = res;
        this.serviceItemInfo.itemTitle = itemName
        this.showServiceItemPopup = true;
      }
    },
    getProductInfo(row, column, event) {
      this.getEsptProductInfo(row);
    },

    async getEsptProductInfo(row) {
      const res = await esptProductInfo(row);
      if (res) {
        // console.log(res);
        this.productInfo.productCode = res.productCode;
        this.productInfo.productName = res.productName;
        this.productInfo.servicePeriod = res.servicePeriod;
        this.productInfo.servicePeriodUnit = res.servicePeriodUnit;
        this.productInfo.minAge = res.minAge;
        this.productInfo.maxAge = res.maxAge;
        this.productInfo.startDay = res.startDay;
        this.productInfo.endDay = res.endDay;
        this.productInfo.headers = [];
        this.productInfo.dataList = [];
        // 对不同类型分开处理，每类的数据不一样
        if (res.planInfoList) {
          // 该类型第一行为头部
          this.productInfo.headers = res.planInfoList[0];
          for (var i = 1; i < res.planInfoList.length; i += 1) {
            this.productInfo.dataList.push(res.planInfoList[i]);
          }
        } else if (res.serviceItemInfoList) {
          this.productInfo.headers = ["服务项目/计划"];
          for (var i = 0; i < res.serviceItemInfoList.length; i += 1) {
            let item = [res.serviceItemInfoList[i]];
            this.productInfo.dataList.push(item);
          }
        } else if (res.rescueServiceItemInfoList) {
          this.productInfo.headers = ["服务项目/计划", "内容"];
          // 救援项目
          for (var i = 0; i < res.rescueServiceItemInfoList.length; i += 1) {
            let itemArray = res.rescueServiceItemInfoList[i].itemList;

            // 找到类型对应的字典作为头部
            let typeCode = res.rescueServiceItemInfoList[i].typeCode;
            if (typeCode) {
              typeCode = typeCode.toString();
              this.rescueServiceItemType.forEach(item => {
                // console.log(item.dicItemId +"  "+typeCode+(item.dicItemId == typeCode));
                if (item.dicItemCode == typeCode) {
                  itemArray.unshift(item.dicItemName);
                }
              });
            }
            this.productInfo.dataList.push(itemArray);
          }

          console.log(this.productInfo);
        }


      }
    },
    opendProductPopup() {
      this.productInfo = {
        productCode: "",
        productName: "",
        servicePeriod: null,
        servicePeriodUnit: "",
        minAge: null,
        maxAge: null,
        startDay: null,
        endDay: null,
        headers: [],
        dataList: []
      };
      this.showProductPopup = true;
    },
    async getEsptProductList() {
      const res = await esptProductList();
      if (res) {
        if (res.healthProductList) {
          this.healthProductList = res.healthProductList;
        }
        if (res.rescueProductList) {
          this.rescueProductList = res.rescueProductList;
        }
      }

    },


  }
};
</script>

<style lang="less" scoped>
.location-box {
  width: 360px;
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  cursor: pointer;
  margin: 5px 5px 5px 5px;

  .left {
    cursor: pointer;

    .location {
      margin: 5px 5px 5px 5px;
    }
  }

  .right {
    cursor: pointer;
  }
}

.member-manage {
  .text-content {
    width: 100%;
    text-align: left;
  }

  .text-content div {
    margin: 5px 5px 5px 5px;
  }

  .communication-item {
    font-weight: 500;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;

      i {
        margin-right: 2px;
      }
    }
  }

  .log-form {
    padding-bottom: 20px;
    height: 650px;
    /* 设置弹窗固定高度 */
    display: flex;
    flex-direction: column;

    .form-content {
      flex: 1;
      overflow-y: auto;
      /* 添加滚动条 */
      padding-right: 10px;
      /* 为滚动条留出空间 */
      margin-bottom: 20px;

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }

      /* Firefox 隐藏滚动条 */
      scrollbar-width: none;

      /* IE 隐藏滚动条 */
      -ms-overflow-style: none;
    }

    .upload-section {
      margin-top: 10px;

      .upload-area {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        text-align: center;
        background: #fafafa;
        cursor: pointer;
        transition: border-color 0.3s;

        .upload-placeholder {
          color: #8c939d;
          font-size: 14px;

          i {
            font-size: 28px;
            margin-bottom: 8px;
            display: block;
          }
        }
      }

      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;

        .image-item {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          border: 1px solid #e4e7ed;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .image-actions {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
              background: rgba(0, 0, 0, 0.8);
            }

            i {
              color: #fff;
              font-size: 12px;
            }
          }
        }
      }
    }

    .form-actions {
      flex-shrink: 0;
      /* 防止按钮区域被压缩 */
      padding-top: 20px;
      text-align: center;
      border-top: 1px solid #e4e7ed;
      background: #fff;
    }
  }

  .delete-confirm {
    padding: 0 20px 20px 20px;
    text-align: center;

    .confirm-content {
      margin-bottom: 20px;

      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }
}

.productContainer {
  display: flex;
  /* 启用Flexbox */
  width: 100%;
  height: 600px;
  /* 设置弹窗固定高度 */
  overflow: hidden;
  /* 防止整体出现滚动条 */
}

.product-left {
  width: 30%;
  /* 或者具体的像素值 */
  height: 100%;
  overflow-y: auto;
  /* 添加垂直滚动 */
  padding-right: 10px;
  /* 为滚动条留出空间 */

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 表格样式调整 */
  .el-table {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.product-right {
  width: 70%;
  /* 或者具体的像素值 */
  height: 100%;
  overflow-y: auto;
  /* 添加垂直滚动 */
  padding-left: 20px;
  /* 为左侧留出间距 */
  padding-right: 10px;
  /* 为滚动条留出空间 */

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

.product-right .productInfo {
  width: 100%;

  .title {
    color: black;
    font-weight: bolder;
    font-size: 16px;
    width: 100%;
    text-align: center;
    margin: 10px 10px 10px 10px;
  }

  .sub_title {
    margin-top: 10px;
  }

  .sub_title span {

    margin-right: 50px;
  }

  .content {
    margin-top: 10px;
    width: 100%;
    overflow: hidden;
    
    /* 响应式表格 */
    @media (max-width: 768px) {
      table {
        width: 100%;
      }
    }
  }

  .content table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
    table-layout: fixed;
    min-width: 0;
    max-width: 100%;
    font-size: 13px;
  }

  .content table th {
    text-align: center;
    border: 1px solid #e4e7ed;
    padding: 12px 8px;
    background-color: #f5f7fa;
    font-weight: 600;
    color: #303133;
    word-break: break-word;
    vertical-align: middle;
  }

  .content table th:first-child {
    text-align: left;
    padding-left: 12px;
    padding-right: 12px;
    white-space: normal;
    word-break: break-word;
    min-width: 160px;
  }

  .content table td {
    text-align: center;
    border: 1px solid #e4e7ed;
    padding: 12px 8px;
    word-break: break-word;
    vertical-align: middle;
    line-height: 1.5;
  }

  .content table td:first-child {
    text-align: left;
    padding-left: 12px;
    padding-right: 12px;
    white-space: normal;
    word-break: break-word;
    min-width: 160px;
  }

  .content table td:not(:first-child) {
    text-align: center;
    padding: 8px 4px;
    font-size: 13px;
  }

  .content table td a {
    color: #409eff;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.3s;
    
    &:hover {
      color: #66b1ff;
      text-decoration: underline;
    }
  }

  .content table td span {
    display: inline-block;
    width: 100%;
    min-height: 20px;
  }

  .content table td:empty::before {
    content: '\00a0';
  }

  .content table tbody tr {
    transition: background-color 0.3s ease;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    &:nth-child(even) {
      background-color: #fafafa;
    }
  }

  .content-items {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: flex-start;
    align-items: center;
    padding: 4px 0;
    width: 100%;
  }

  .content-item {
    display: inline-block;
    padding: 2px 6px;
    background-color: #f0f9ff;
    border-radius: 4px;
    font-size: 12px;
    white-space: normal;
    word-break: break-word;
    max-width: 100%;
    
    a {
      color: #409eff;
      text-decoration: none;
      cursor: pointer;
      transition: color 0.3s;
      
      &:hover {
        color: #66b1ff;
        text-decoration: underline;
      }
    }
  }
}

.serviceContainer {
  width: 100%;
  margin-bottom: 20px;

  .serviceItem {
    margin-top: 0px;

  }

  .serviceItem .title {
    font-weight: bold;
  }

}
</style>

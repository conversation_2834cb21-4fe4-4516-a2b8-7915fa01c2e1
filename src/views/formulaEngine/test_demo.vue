<template>
  <InfoPageLayout
    :title="pageTitle"
    subtitle="公式测试与计算"
    icon="el-icon-cpu"
    :breadcrumbItems="breadcrumbItems"
  >
    <!-- 公式信息卡片 -->
    <InfoCard title="公式信息" icon="el-icon-document">
      <el-row :gutter="20">
        <el-col :span="8">
          <InfoItem label="公式名称" :value="formulaData.name" />
        </el-col>
        <el-col :span="8">
          <InfoItem label="版本号" :value="formulaData.version || '1.0'" />
        </el-col>
        <el-col :span="8">
          <InfoItem label="分类">
              <el-tag :type="getCategoryTagType(formulaData.category)" size="medium">
                {{ formulaData.categoryName }}
              </el-tag>
          </InfoItem>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 10px;">
        <el-col :span="24">
          <InfoItem label="公式内容">
                  <div class="formula-content">{{ formulaData.formula }}</div>
          </InfoItem>
        </el-col>
      </el-row>
      <el-row v-if="formulaData.description" :gutter="20" style="margin-top: 10px;">
        <el-col :span="24">
          <InfoItem label="公式描述" :value="formulaData.description" />
        </el-col>
      </el-row>
    </InfoCard>

    <!-- 参数配置卡片 -->
    <InfoCard v-if="formulaData.variables && formulaData.variables.length > 0" title="参数配置" icon="el-icon-setting">
      <el-row :gutter="20">
        <el-col v-for="variable in formulaData.variables" :key="variable.name" :span="8">
          <div class="parameter-item">
            <div class="parameter-header">
              <span class="parameter-name">{{ variable.name }}</span>
              <span class="parameter-type" :class="variable.type">{{ getVariableTypeText(variable.type) }}</span>
            </div>
                  <el-input-number
                    v-model="parameters[variable.name]"
                    :precision="getPrecision(variable.type)"
                    :step="getStep(variable.type)"
                    :min="variable.minValue"
                    :max="variable.maxValue"
                    @change="handleParameterChange"
                    style="width: 100%"
                  />
                <div class="parameter-description" v-if="variable.description">
                  {{ variable.description }}
            </div>
          </div>
        </el-col>
      </el-row>
      <div style="margin-top: 16px; text-align: right;">
        <el-button class="action-btn reset-btn" size="small" @click="resetParameters">
          <i class="el-icon-refresh"></i>重置
        </el-button>
        <el-button class="action-btn calculate-btn" size="small" @click="calculateFormula" :loading="calculating">
          <i class="el-icon-cpu"></i>{{ calculating ? '计算中...' : '计算' }}
              </el-button>
            </div>
    </InfoCard>

    <!-- 计算结果卡片 -->
    <InfoCard v-if="calculationResult" title="计算结果" icon="el-icon-data-analysis">
      <div v-if="calculationResult.status === 1">
        <el-row :gutter="20">
          <el-col :span="8">
            <InfoItem label="计算结果">
              <span class="result-value">{{ formatNumber(calculationResult.result) }}</span>
            </InfoItem>
          </el-col>
          <el-col :span="8">
            <InfoItem label="执行时间" :value="calculationResult.executionTimeMs + 'ms'" />
          </el-col>
          <el-col :span="8">
            <InfoItem label="计算时间" :value="formatTime(calculationResult.calculationTime)" />
          </el-col>
        </el-row>
          </div>
      <div v-else class="result-error">
        <el-alert
          title="计算失败"
          :description="calculationResult.errorMessage"
          type="error"
          show-icon
        />
                </div>
    </InfoCard>

    <!-- 历史记录卡片 -->
    <InfoCard title="计算历史" icon="el-icon-time">
      <el-table :data="calculationHistory" stripe class="modern-table" style="width: 100%;">
        <el-table-column label="#" type="index" width="50" />
        <el-table-column prop="formulaName" label="公式名称" width="150">
          <template slot-scope="scope">
            {{ scope.row.formulaName || formulaData.name }}
          </template>
        </el-table-column>
        <el-table-column label="输入参数">
          <template slot-scope="scope">
            <span v-for="(value, key) in parseInputVariables(scope.row.inputVariables)" :key="key" class="param-item">
              {{ key }} = {{ formatNumber(value) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="result" label="计算结果" width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.status === 1 || scope.row.status === 'success'" class="result-value">
              {{ formatNumber(scope.row.result || scope.row.calculationResult) }}
            </span>
            <span v-else class="result-error">
              {{ scope.row.errorMessage || scope.row.error || '计算失败' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="calculationTime" label="计算时间" width="180">
          <template slot-scope="scope">
            {{ formatTime(scope.row.calculationTime || scope.row.createTime || scope.row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="(scope.row.status === 1 || scope.row.status === 'success') ? 'success' : 'danger'" size="small">
              {{ (scope.row.status === 1 || scope.row.status === 'success') ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="removeHistory(scope.$index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="calculationHistory.length === 0" class="no-history">
              <div class="empty-icon">
                <i class="el-icon-document"></i>
              </div>
              <div class="empty-text">暂无计算历史记录</div>
            </div>
      <div style="margin-top: 10px; text-align: right;">
        <el-button class="action-btn" size="small" @click="clearHistory" v-if="calculationHistory.length > 0">
          <i class="el-icon-refresh"></i>刷新历史
        </el-button>
      </div>
    </InfoCard>

      <!-- 无公式数据时的提示 -->
    <InfoCard v-if="!loading && !formulaData.id" title="提示" icon="el-icon-warning-outline">
        <div class="empty-content">
          <div class="empty-icon">
            <i class="el-icon-warning-outline"></i>
          </div>
          <div class="empty-text">
            <h3>未找到公式数据</h3>
            <p>请确保已正确配置公式信息</p>
          </div>
        </div>
    </InfoCard>
  </InfoPageLayout>
</template>

<script>
import InfoPageLayout from '@/components/layouts/InfoPageLayout'
import InfoCard from '@/components/layouts/InfoCard'
import InfoItem from '@/components/layouts/InfoItem'
import { calculateFormula, getFormulaCalculationLogs, getFormulaDetail } from '@/api/formulaEngine'

export default {
  name: 'FormulaTestDemo',
  components: {
    InfoPageLayout,
    InfoCard,
    InfoItem
  },
  data() {
    return {
      loading: true,
      calculating: false,
      formulaData: {},
      parameters: {},
      calculationResult: null,
      calculationHistory: [],
      pageTitle: '公式测试',
      breadcrumbItems: [
        { text: '公式管理', icon: 'el-icon-data-analysis', to: '/formulaEngine' },
        { text: '公式测试', icon: 'el-icon-cpu' }
      ]
    }
  },
  computed: {
    formulaId() {
      return this.$route.params.id
    }
  },
  mounted() {
    this.loadFormulaData()
  },
  methods: {
    async loadFormulaData() {
      if (!this.formulaId) {
        this.$message.error('缺少公式ID参数')
        this.$router.push('/formulaEngine')
        return
      }

      this.loading = true
      try {
        await this.loadFormulaDetail()
        this.initializeParameters()
        // 加载公式数据后，加载历史记录
        await this.loadCalculationHistory()
      } catch (error) {
        this.$message.error('加载公式数据失败')
        console.error('加载公式数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    async loadFormulaDetail() {
      // 使用真实的公式 ID 调用 API
      console.log('加载公式 ID:', this.formulaId, '类型:', typeof this.formulaId)

      // 确保 ID 是数字类型
      const formulaId = parseInt(this.formulaId)
      if (isNaN(formulaId)) {
        throw new Error('无效的公式ID: ' + this.formulaId)
      }

      try {
        const response = await getFormulaDetail(formulaId)
        console.log('API 响应:', response)

        if (response) {
          this.formulaData = {
            ...response,
            id: formulaId // 确保 ID 正确
          }
          console.log('加载的公式数据:', this.formulaData)
        } else {
          throw new Error(response?.message || '加载公式失败')
        }
      } catch (error) {
        console.error('加载公式详情失败:', error)
        this.$message.error('加载公式详情失败: ' + (error.message || '未知错误'))
        // 不使用模拟数据，直接抛出错误
        throw error
      }
    },
    initializeParameters() {
      // 创建新的参数对象
      const newParameters = {}
      if (this.formulaData && this.formulaData.variables) {
        this.formulaData.variables.forEach(variable => {
          newParameters[variable.name] = variable.defaultValue || 0
        })
      }

      // 使用 Vue.set 或直接赋值来触发响应式更新
      this.parameters = { ...newParameters }
    },
    resetParameters() {
      console.log('重置前的参数:', JSON.parse(JSON.stringify(this.parameters)))
      this.initializeParameters()
      console.log('重置后的参数:', JSON.parse(JSON.stringify(this.parameters)))

      // 强制更新视图
      this.$nextTick(() => {
        this.$forceUpdate()
      })

      this.$message.success('参数已重置为默认值')
    },
    handleParameterChange() {},
    getPrecision(type) {
      return type === 'constant' ? 4 : 2
    },
    getStep(type) {
      return type === 'constant' ? 0.01 : 0.1
    },
    async calculateFormula() {
      if (!this.formulaData || !this.formulaData.id) {
        this.$message.warning('公式数据未加载')
        return
      }
      this.calculating = true
      try {
        const response = await calculateFormula({
          formulaId: this.formulaData.id,
          variables: this.parameters
        })
        if (response) {
          this.calculationResult = response
          this.addToHistory(this.calculationResult)
          this.$message.success('计算完成')
        } else {
          this.$message.error(response?.message || '计算失败')
        }
      } catch (error) {
        this.$message.error('计算请求失败')
        console.error('计算失败:', error)
      } finally {
        this.calculating = false
      }
    },

    // 加载计算历史记录
    async loadCalculationHistory() {
      if (!this.formulaData || !this.formulaData.id) {
        return;
      }

      try {
        const response = await getFormulaCalculationLogs(this.formulaData.id, 10);

        // 根据后端返回的数据结构处理
        let historyData = []
        if (response && response.code === 200) {
          historyData = response.data || []
        } else if (Array.isArray(response)) {
          historyData = response
        }

        // 格式化历史记录数据
        this.calculationHistory = historyData.map(item => ({
          ...item,
          // 确保有正确的字段名
          formulaName: item.formulaName || this.formulaData.name,
          inputVariables: item.inputVariables, // 保持原始格式，在显示时解析
          result: item.result || item.calculationResult,
          status: item.status || (item.result !== undefined ? 1 : 0),
          calculationTime: item.calculationTime || item.createTime || item.timestamp
        }));

        console.log('格式化后的历史记录:', this.calculationHistory)
      } catch (error) {
        console.error('加载计算历史失败:', error);
        this.$message.error('加载计算历史失败');
      }
    },

    addToHistory(result) {
      // 计算完成后延迟重新加载历史记录，确保后端数据已保存
      setTimeout(() => {
        this.loadCalculationHistory();
      }, 1000);
    },
    removeHistory(index) {
      // 暂时禁用删除功能，因为没有删除单条记录的 API
      this.$message.info('删除功能暂未开放');
    },
    clearHistory() {
      // 重新加载历史记录（刷新）
      this.loadCalculationHistory();
      this.$message.success('历史记录已刷新');
    },
    getVariableTypeText(type) {
      const typeMap = {
        number: '数值',
        variable: '变量',
        constant: '常数'
      }
      return typeMap[type] || type
    },
    getCategoryTagType(category) {
      const typeMap = {
        1: 'primary',
        2: 'success',
        3: 'warning',
        4: 'info'
      }
      return typeMap[category] || 'default'
    },
    formatNumber(value) {
      if (value === null || value === undefined) return '-'
      if (typeof value === 'number') {
        return value.toFixed(4)
      }
      return Number(value).toFixed(4)
    },
    formatTime(time) {
      if (!time) return '-'

      // 处理不同格式的时间
      let date
      if (typeof time === 'string') {
        date = new Date(time)
      } else if (typeof time === 'number') {
        // 如果是时间戳，检查是否需要转换为毫秒
        date = new Date(time > 1000000000000 ? time : time * 1000)
      } else {
        date = new Date(time)
      }

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '-'
      }

      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 解析输入参数
    parseInputVariables(inputVariables) {
      if (!inputVariables) {
        return {}
      }

      // 如果已经是对象，直接返回
      if (typeof inputVariables === 'object' && inputVariables !== null) {
        return inputVariables
      }

      // 如果是字符串，尝试解析 JSON
      if (typeof inputVariables === 'string') {
        try {
          return JSON.parse(inputVariables)
        } catch (error) {
          console.warn('解析输入参数失败:', error, inputVariables)
          return {}
        }
      }

      return {}
    }
  }
}
</script>

<style lang="less" scoped>
          .formula-content {
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            color: #333;
            border-left: 3px solid #FF8030;
            line-height: 1.6;
            word-break: break-all;
          }
      .parameter-item {
        background: #fafafa;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        padding: 16px;
  margin-bottom: 16px;
        .parameter-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
    margin-bottom: 8px;
          .parameter-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            font-family: 'Consolas', 'Monaco', monospace;
          }
          .parameter-type {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            &.number {
              background: #e6f7ff;
              color: #1890ff;
            }
            &.variable {
              background: #f6ffed;
              color: #52c41a;
            }
            &.constant {
              background: #fff2e8;
              color: #FF8030;
            }
          }
        }
        .parameter-description {
          font-size: 12px;
          color: #999;
          line-height: 1.4;
    margin-top: 4px;
  }
}
        .result-value {
  font-size: 18px;
            font-weight: 700;
            color: #52c41a;
            font-family: 'Consolas', 'Monaco', monospace;
          }
    .result-error {
            color: #ff4d4f;
            font-size: 14px;
}
              .param-item {
                background: #fff;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;
                color: #333;
                font-family: 'Consolas', 'Monaco', monospace;
                border: 1px solid #e8e8e8;
  margin-right: 4px;
}
    .no-history {
      text-align: center;
      padding: 40px 20px;
      .empty-icon {
        margin-bottom: 16px;
        i {
          font-size: 48px;
          color: #d9d9d9;
        }
      }
      .empty-text {
        font-size: 14px;
        color: #999;
      }
    }
    .empty-content {
      text-align: center;
      .empty-icon {
        margin-bottom: 24px;
        i {
          font-size: 64px;
          color: #d9d9d9;
        }
      }
      .empty-text {
        h3 {
          margin: 0 0 8px 0;
          font-size: 18px;
          color: #666;
        }
        p {
          margin: 0;
          font-size: 14px;
          color: #999;
        }
      }
    }
.modern-table {
  /deep/ .el-table__header-wrapper {
    th {
      background: #f7ecdd;
      font-weight: 600;
      color: #2c3e50;
    }
  }
}
</style>

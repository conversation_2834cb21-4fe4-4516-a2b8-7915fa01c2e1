<template>
  <div class="formula-engine-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="公式管理"
      subtitle="管理和配置业务公式，支持公式创建、编辑、测试和版本控制"
      title-icon="el-icon-data-analysis"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="320"
      :search-label-width="'100px'"
      add-button-text="新增公式"
      empty-title="暂无公式数据"
      empty-description="点击上方新增公式按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 公式名称列插槽 -->
      <template #name="{ row }">
        <div class="name-cell">
          <div class="name-icon">
            <i class="el-icon-data-analysis"></i>
          </div>
          <span class="name-text">{{ row.name }}</span>
        </div>
      </template>

      <!-- 公式内容列插槽 -->
      <template #formula="{ row }">
        <div class="formula-cell">
          <div class="formula-content" :title="row.formula">
            {{ row.formula }}
          </div>
        </div>
      </template>

      <!-- 企业类型列插槽 -->
      <template #enterpriseType="{ row }">
        <div v-if="getDisplayEnterpriseTypes(row).length > 0">
          <el-tag
            v-for="type in getDisplayEnterpriseTypes(row)"
            :key="type"
            :type="getEnterpriseTypeTagType(type)"
            size="small"
            class="enterprise-type-tag"
            style="margin-right: 4px; margin-bottom: 4px;"
          >
            {{ getEnterpriseTypeName(type) }}
          </el-tag>
        </div>
        <span v-else>全部类型</span>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag
          :type="row.status === 1 ? 'success' : 'danger'"
          :class="row.status === 1 ? 'status-enabled' : 'status-disabled'"
          size="small"
        >
          <i :class="row.status === 1 ? 'el-icon-check' : 'el-icon-close'"></i>
          {{ row.status === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <!-- 描述列插槽 -->
      <template #description="{ row }">
        <div class="description-cell">
          <span class="description-text" :title="row.description">
            {{ row.description || '暂无描述' }}
          </span>
        </div>
      </template>

      <!-- 更新时间列插槽 -->
      <template #updateTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.updateTime }}
        </div>
      </template>
    </UniversalTable>

    <!-- 删除确认弹窗（统一组件） -->
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该公式？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script>
import { getFormulaList, deleteFormula } from '@/api/formulaEngine'
import { getEnterpriseTypeOptions } from '@/api/enterprise/type'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'FormulaEngine',
  components: {
    UniversalTable,
    ConfirmDialog
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        param: {
          name: '',
          enterpriseType: [],
          status: ''
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      enterpriseTypeOptions: [],
      currentRow: null, // 用于存储当前操作行
      // 表格列配置
      tableColumns: [
        {
          prop: 'name',
          label: '公式名称',
          minWidth: 200,
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'formula',
          label: '公式内容',
          minWidth: 300,
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'enterpriseType',
          label: '企业类型',
          minWidth: 140,
          align: 'center'
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          align: 'center'
        },
        {
          prop: 'description',
          label: '描述',
          minWidth: 200,
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          width: 180,
          align: 'center'
        }
      ],
      // 操作按钮配置
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        },
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          class: 'edit-btn',
          size: 'mini'
        },
        {
          key: 'test',
          label: '测试',
          icon: 'el-icon-cpu',
          class: 'config-btn',
          size: 'mini'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn',
          size: 'mini'
        }
      ],
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '公式名称',
          name: 'name',
          type: 'input',
          placeholder: '请输入公式名称'
        },
        {
          label: '企业类型',
          name: 'enterpriseType',
          type: 'select',
          placeholder: '请选择企业类型',
          multiple: true,
          collapseTags: true,
          list: []
        },
        {
          label: '状态',
          name: 'status',
          type: 'select',
          placeholder: '请选择状态',
          list: [
            { dicItemName: '启用', dicItemCode: 1 },
            { dicItemName: '禁用', dicItemCode: 0 }
          ]
        }
      ]
    }
  },



  mounted() {
    this.loadData()
    this.loadEnterpriseTypeOptions()
  },

  methods: {
    // 加载表格数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: {
            ...this.searchForm.param,
          }
        };
        const response = await getFormulaList(params)
        if (response) {
          this.tableData = response.list
          this.pagination.total = response.total
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },


    async loadEnterpriseTypeOptions() {
      try {
        const response = await getEnterpriseTypeOptions()
        if (response) {
          this.enterpriseTypeOptions = response
          // 更新搜索表单配置中的选项
          const typeConfig = this.searchFormConfig.find(item => item.name === 'enterpriseType')
          if (typeConfig) {
            // 转换为SearchForm组件期望的格式
            typeConfig.list = response.map(item => ({
              dicItemName: item.name,
              dicItemCode: item.code
            }))
          }
        } else {
          console.error('加载企业类型选项失败，响应:', response)
          this.enterpriseTypeOptions = []
          const typeConfig = this.searchFormConfig.find(item => item.name === 'enterpriseType')
          if (typeConfig) {
            typeConfig.list = []
          }
          this.$message.error(response?.message || '加载企业类型选项失败')
        }
      } catch (error) {
        console.error('加载企业类型选项失败:', error)
        this.enterpriseTypeOptions = []
        const typeConfig = this.searchFormConfig.find(item => item.name === 'enterpriseType')
        if (typeConfig) {
          typeConfig.list = []
        }
        this.$message.error('加载企业类型选项失败')
      }
    },

    // 处理操作按钮点击
    handleAction({ action, row }) {
      this.currentRow = row

      switch (action) {
        case 'view':
          this.handleView(row)
          break
        case 'edit':
          this.handleEdit(row)
          break
        case 'test':
          this.handleTest(row)
          break
        case 'delete':
          this.$refs.confirmDialog.show(`确定要删除公式「${row.name}」吗？`)
          break
      }
    },

    handleSearch(searchData) {
      this.searchForm = { ...searchData }
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleReset() {
      this.searchForm = {
        param: {
          name: '',
          enterpriseType: [],
          status: ''
        }
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleAdd() {
      this.$router.push({
        name: 'formulaEngineAdd',
        query: { mode: 'add', from: 'list' }
      })
    },

    handleView(row) {
      this.$router.push({
        name: 'formulaEngineView',
        params: { id: row.id },
        query: { mode: 'view', from: 'list' }
      })
    },

    handleEdit(row) {
      this.$router.push({
        name: 'formulaEngineEdit',
        params: { id: row.id },
        query: { mode: 'edit', from: 'list' }
      })
    },

    handleTest(row) {
      console.log('测试按钮点击，行数据:', row)
      console.log('传递的 ID:', row.id)
      this.$router.push({
        name: 'formulaEngineTest',
        params: { id: row.id }
      })
    },

    async confirmDelete() {
      try {
        const response = await deleteFormula(this.currentRow.id)
        if (response) {
          this.$message.success('删除成功')
          this.loadData()
        } else {
          this.$message.error(response.message || '删除失败')
        }
      } catch (error) {
        this.$message.error('删除失败')
      }
    },


    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },

    // 获取企业类型标签类型
    getEnterpriseTypeTagType(type) {
      const typeMap = {
        'A': 'primary',
        'B': 'success',
        'C': 'info',
        'D': 'warning',
        'E': 'danger'
      }
      return typeMap[type] || 'default'
    },

    // 获取企业类型名称
    getEnterpriseTypeName(code) {
      const enterpriseType = this.enterpriseTypeOptions.find(item => item.code === code)
      return enterpriseType ? enterpriseType.name : code
    },

    // 获取要显示的企业类型列表
    getDisplayEnterpriseTypes(row) {
      // 优先使用 enterpriseTypeList，如果没有则处理 enterpriseType
      if (row.enterpriseTypeList && Array.isArray(row.enterpriseTypeList)) {
        return row.enterpriseTypeList
      } else if (row.enterpriseType) {
        if (typeof row.enterpriseType === 'string') {
          return row.enterpriseType.split(',').filter(type => type.trim())
        } else if (Array.isArray(row.enterpriseType)) {
          return row.enterpriseType
        }
      }
      return []
    }
  }
}
</script>

<style lang="less" scoped>
.formula-engine-container {
  min-height: 100vh;
  background: white;
  overflow: hidden;

  .name-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .name-icon {
      color: #D7A256;
      font-size: 16px;
    }

    .name-text {
      font-weight: 500;
    }
  }

  .formula-cell {
    .formula-content {
      max-width: 280px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-family: 'Courier New', monospace;
      background: #f8f9fa;
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid #e9ecef;
    }
  }

  .enterprise-type-tag {
    margin-right: 4px;
    margin-bottom: 4px;
  }

  .status-enabled {
    background: #f0f9ff;
    border-color: #67c23a;
    color: #67c23a;
  }

  .status-disabled {
    background: #fef0f0;
    border-color: #f56c6c;
    color: #f56c6c;
  }

  .description-cell {
    .description-text {
      color: #666;
      font-size: 13px;
    }
  }

  .time-cell {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #999;
    font-size: 13px;
  }
}
</style>

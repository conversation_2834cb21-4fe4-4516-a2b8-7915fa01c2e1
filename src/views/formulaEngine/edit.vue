<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <!-- 基本信息表单 -->
    <UniversalForm
      ref="universalForm"
      :form-data="formData"
      :form-rules="formRules"
      :form-groups="basicFormGroups"
      :is-view="isView"
      :loading="loading"
    />

    <!-- 公式编辑器 -->
    <div class="universal-form-container" style="margin-top: -30px;">
      <div class="universal-form">
        <div class="form-group">
          <div class="group-title">
            <div class="title-left">
              公式编辑器
            </div>

          </div>

          <div class="formula-editor-content">
            <div class="formula-editor-block">

          <!-- 工具栏 -->
          <div class="editor-section">
            <div class="editor-toolbar" v-if="!isView">
              <div class="toolbar-section">
                <span class="toolbar-label">函数:</span>
                <el-button-group class="function-buttons">
                  <el-button
                    v-for="func in mathFunctions"
                    :key="func.name"
                    size="mini"
                    @click="insertFunction(func)"
                    :title="func.description"
                    class="function-btn"
                  >
                    {{ func.name }}
                  </el-button>
                </el-button-group>
              </div>

              <div class="toolbar-section">
                <span class="toolbar-label">运算符:</span>
                <el-button-group class="operator-buttons">
                  <el-button
                    v-for="op in operators"
                    :key="op.symbol"
                    size="mini"
                    @click="insertOperator(op)"
                    :title="op.description"
                    class="operator-btn"
                  >
                    {{ op.symbol }}
                  </el-button>
                </el-button-group>
              </div>

              <div class="toolbar-section">
                <span class="toolbar-label">常数:</span>
                <el-button-group class="constant-buttons">
                  <el-button
                    v-for="constant in constants"
                    :key="constant.name"
                    size="mini"
                    @click="insertConstant(constant)"
                    :title="constant.description"
                    class="constant-btn"
                  >
                    {{ constant.name }}
                  </el-button>
                </el-button-group>
              </div>

              <div class="toolbar-section">
                <span class="toolbar-label">输入变量:</span>
                <el-button-group class="variable-buttons">
                  <el-button
                    v-for="variable in formData.variables"
                    :key="variable.name"
                    size="mini"
                    @click="insertVariable(variable)"
                    :title="variable.description || variable.name"
                    class="variable-btn"
                    v-if="variable.name"
                  >
                    {{ variable.name }}
                  </el-button>
                  <el-button
                    v-if="!formData.variables || formData.variables.length === 0"
                    size="mini"
                    disabled
                    class="variable-btn"
                  >
                    暂无变量
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </div>

          <!-- 使用说明 -->
          <div class="editor-section">
            <div class="editor-guide-inline" @click="toggleGuide" :class="{hover: guideHover}" @mouseenter="guideHover=true" @mouseleave="guideHover=false">
              <i class="el-icon-info"></i>
              <span class="guide-title">公式编辑器使用说明</span>
              <i class="el-icon-arrow-down guide-arrow" :class="{ 'is-expanded': showGuide }"></i>
            </div>
            <transition name="fade">
              <div v-show="showGuide" class="guide-content">
                <div class="guide-details">
                  <h5>函数说明：</h5>
                  <ul>
                    <li><b>sin(x)</b> - 正弦函数，计算角度的正弦值</li>
                    <li><b>cos(x)</b> - 余弦函数，计算角度的余弦值</li>
                    <li><b>tan(x)</b> - 正切函数，计算角度的正切值</li>
                    <li><b>log(x)</b> - 对数函数，计算以10为底的对数</li>
                    <li><b>ln(x)</b> - 自然对数，计算以e为底的对数</li>
                    <li><b>sqrt(x)</b> - 平方根，计算数值的平方根</li>
                    <li><b>abs(x)</b> - 绝对值，返回数值的绝对值</li>
                    <li><b>exp(x)</b> - 指数函数，计算e的幂次方</li>
                    <li><b>pow(x, n)</b> - 幂函数，计算x的n次幂</li>
                    <li><b>factorial(x)</b> - 阶乘函数，计算数值的阶乘</li>
                  </ul>
                  <h5>运算符说明：</h5>
                  <ul>
                    <li><b>+</b> - 加法运算</li>
                    <li><b>-</b> - 减法运算</li>
                    <li><b>*</b> - 乘法运算</li>
                    <li><b>/</b> - 除法运算</li>
                    <li><b>^</b> - 幂运算（等同于pow函数）</li>
                    <li><b>( )</b> - 括号，用于分组运算</li>
                    <li><b>!</b> - 阶乘运算（等同于factorial函数）</li>
                  </ul>
                  <h5>常数说明：</h5>
                  <ul>
                    <li>常数由"常数设置"模块统一维护，如PI、E、a、b、c等</li>
                    <li>在公式中可直接使用常数名称，如：a * x + b * sqrt(x)</li>
                    <li>常数会自动从常数设置模块加载，状态为"启用"的常数才会显示</li>
                    <li>点击工具栏"常数"按钮可快速插入常数</li>
                    <li>常数A、B、C、D、E是风险评分计算的专用系数，可直接在公式中使用</li>
                  </ul>
                  <h5>输入变量说明：</h5>
                  <ul>
                    <li>输入变量需要在下方"变量配置"中定义</li>
                    <li>变量类型分为"数值"和"变量"两种</li>
                    <li>"数值"类型：固定数值，如权重系数</li>
                    <li>"变量"类型：动态输入值，如问卷得分</li>
                    <li>点击工具栏"输入变量"按钮可快速插入已定义的变量</li>
                    <li>变量名建议使用字母，如x、y、score等</li>
                  </ul>
                  <h5>配置步骤：</h5>
                  <div class="config-steps">
                    <ol>
                      <li><b>基本信息配置</b>
                        <ul>
                          <li>填写公式名称和描述</li>
                          <li>选择公式分类</li>
                          <li>设置启用状态</li>
                        </ul>
                      </li>
                      <li><b>公式编写</b>
                        <ul>
                          <li>使用工具栏快速插入函数、运算符、常数、变量</li>
                          <li>或直接在文本框中输入公式表达式</li>
                          <li>公式无需写f(x)=，直接写表达式即可</li>
                          <li>注意括号匹配和语法正确性</li>
                        </ul>
                      </li>
                      <li><b>变量配置</b>
                        <ul>
                          <li>点击"添加变量"按钮</li>
                          <li>填写变量名、类型、默认值、描述</li>
                          <li>变量类型选择"数值"或"变量"</li>
                          <li>可添加多个变量</li>
                        </ul>
                      </li>
                      <li><b>公式验证</b>
                        <ul>
                          <li>点击"验证"检查语法</li>
                          <li>查看验证结果和错误提示</li>
                          <li>根据提示修正公式语法</li>
                        </ul>
                      </li>
                      <li><b>保存配置</b>
                        <ul>
                          <li>确认所有信息无误后点击"保存"</li>
                          <li>系统会保存公式配置到数据库</li>
                        </ul>
                      </li>
                    </ol>
                  </div>
                  <h5>配置示例：</h5>
                  <div class="example-formula">
                    <p><b>示例公式：</b> a * pow(x, 2) + b * sqrt(x) + c * sin(x) + d</p>
                    <p><b>变量配置：</b></p>
                    <ul>
                      <li>x - 类型：变量，默认值：5，描述：输入参数</li>
                      <li>a - 类型：数值，默认值：0.3，描述：二次项系数</li>
                      <li>b - 类型：数值，默认值：0.2，描述：平方根项系数</li>
                      <li>c - 类型：数值，默认值：0.1，描述：正弦项系数</li>
                      <li>d - 类型：数值，默认值：0.4，描述：常数项</li>
                    </ul>
                    <p><b>使用步骤：</b></p>
                    <ol>
                      <li>在"变量配置"中添加上述变量</li>
                      <li>在"公式内容"中输入：a * pow(x, 2) + b * sqrt(x) + c * sin(x) + d</li>
                      <li>点击"验证"检查语法</li>
                      <li>保存公式配置</li>
                    </ol>
                  </div>
                </div>
              </div>
            </transition>
          </div>

          <!-- 公式内容 -->
          <div class="editor-section">
            <el-form
              :model="formData"
              :rules="formRules"
              ref="formulaForm"
              class="formula-form"
            >
              <el-form-item label="公式内容" prop="formula">
                <div class="formula-input-wrapper">
                  <el-input
                    v-model="formData.formula"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入数学公式，例如: a*X^2 + b*sqrt(X) + c"
                    :disabled="isView"
                    class="formula-textarea"
                    ref="formulaInput"
                    @input="onFormulaChange"
                  />
                  <div class="formula-input-actions" v-if="!isView">
                    <el-button
                      type="text"
                      size="small"
                      icon="el-icon-refresh"
                      @click="clearFormula"
                      title="清空公式"
                      class="clear-btn"
                    >
                      清空
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      icon="el-icon-check"
                      @click="validateFormula"
                      title="验证公式"
                      class="validate-btn"
                    >
                      验证
                    </el-button>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>

          <!-- 公式预览 -->
          <div class="editor-section" v-if="formData.formula">
            <div class="formula-preview">
              <div class="preview-header">
                <span class="preview-label">公式预览:</span>
                <el-tag
                  :type="validationResult.valid ? 'success' : 'danger'"
                  size="small"
                  class="validation-tag"
                >
                  <i :class="validationResult.valid ? 'el-icon-check' : 'el-icon-close'"></i>
                  {{ validationResult.valid ? '语法正确' : '语法错误' }}
                </el-tag>
              </div>
              <div class="preview-content">
                <div class="formula-display">
                  {{ formData.formula }}
                </div>
              </div>

              <!-- 验证错误信息 -->
              <div v-if="validationResult.errors && validationResult.errors.length > 0" class="validation-errors">
                <div class="error-header">
                  <i class="el-icon-warning"></i>
                  语法错误:
                </div>
                <ul class="error-list">
                  <li v-for="error in validationResult.errors" :key="error" class="error-item">
                    {{ error }}
                  </li>
                </ul>
              </div>

              <!-- 建议信息 -->
              <div v-if="validationResult.suggestions && validationResult.suggestions.length > 0" class="validation-suggestions">
                <div class="suggestion-header">
                  <i class="el-icon-info"></i>
                  建议:
                </div>
                <ul class="suggestion-list">
                  <li v-for="suggestion in validationResult.suggestions" :key="suggestion" class="suggestion-item">
                    {{ suggestion }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </EditPageContainer>
</template>

<style scoped>
/* 公式编辑器容器样式 */
.universal-form-container {
  padding: 20px;
}

.universal-form .form-group {
  margin-bottom: 32px;
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

/* 编辑器内容区域 */
.editor-section {
  margin-bottom: 20px;
}

.editor-section:last-child {
  margin-bottom: 0;
}

/* 工具栏样式 */
.editor-toolbar {
  background: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
}

.toolbar-section {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.toolbar-section:last-child {
  margin-bottom: 0;
}

.toolbar-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  margin-right: 12px;
  min-width: 60px;
}

.el-button-group .el-button {
  margin-right: 4px;
  font-size: 12px;
  padding: 4px 8px;
}

.el-button-group .el-button:last-child {
  margin-right: 0;
}

/* 工具栏按钮样式 */
.function-btn {
  background: #f8f4f0;
  border-color: #e6c89a;
  color: #CD853F;
}

.function-btn:hover {
  background: #CD853F;
  color: white;
  border-color: #CD853F;
}

.operator-btn {
  background: #f8f4f0;
  border-color: #e6c89a;
  color: #CD853F;
}

.operator-btn:hover {
  background: #CD853F;
  color: white;
  border-color: #CD853F;
}

.constant-btn {
  background: #f8f4f0;
  border-color: #e6c89a;
  color: #CD853F;
}

.constant-btn:hover {
  background: #CD853F;
  color: white;
  border-color: #CD853F;
}

.variable-btn {
  background: #f8f4f0;
  border-color: #e6c89a;
  color: #CD853F;
}

.variable-btn:hover {
  background: #CD853F;
  color: white;
  border-color: #CD853F;
}

.variable-btn:disabled {
  background: #f5f5f5;
  border-color: #e0e0e0;
  color: #bdbdbd;
}

/* 公式输入框样式 */
.formula-input-wrapper {
  position: relative;
}

.formula-textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.formula-input-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
}

.formula-input-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  color: #606266;
}

.formula-input-actions .el-button:hover {
  background: rgba(255, 255, 255, 1);
  color: #D2691E;
}

.formula-input-actions .clear-btn:hover {
  color: #f56c6c;
}

.formula-input-actions .validate-btn:hover {
  color: #D2691E;
}

/* 公式预览样式 */
.formula-preview {
  background: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.preview-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.validation-tag {
  font-size: 12px;
}

.validation-tag i {
  margin-right: 4px;
}

.formula-display {
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  min-height: 80px;
}

.raw-formula {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.formula-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  display: block;
  margin-bottom: 6px;
}

.raw-text {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  color: #606266;
  background: #f8f9fa;
  padding: 6px 8px;
  border-radius: 3px;
  display: inline-block;
}

.rendered-formula .formula-label {
  margin-bottom: 8px;
}

/* 数学表达式样式 */
.math-expression {
  font-size: 18px;
  line-height: 1.8;
  color: #303133;
  padding: 8px 0;
}

.math-func {
  font-style: normal;
  font-weight: 500;
  color: #D2691E;
  margin-right: 2px;
}

.math-args {
  color: #303133;
}

.math-sup {
  font-size: 0.75em;
  vertical-align: super;
  color: #606266;
}

.math-sqrt {
  position: relative;
  color: #D2691E;
  font-weight: bold;
}

.math-sqrt-content {
  border-top: 1px solid #D2691E;
  padding-top: 2px;
  margin-left: 2px;
}

.math-abs {
  color: #D2691E;
  font-weight: bold;
}

.math-factorial {
  color: #D2691E;
  font-weight: bold;
  font-size: 1.2em;
}

.math-operator {
  color: #606266;
  margin: 0 4px;
  font-weight: 500;
}

.math-constant {
  color: #B8860B;
  font-style: italic;
  font-weight: 500;
}

.math-variable {
  color: #A0522D;
  font-style: italic;
  font-weight: 500;
}

.math-base {
  color: #303133;
}

/* 验证错误和建议样式 */
.validation-errors,
.validation-suggestions {
  margin-top: 12px;
  padding: 12px;
  border-radius: 4px;
}

.error-header,
.suggestion-header {
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.error-header i,
.suggestion-header i {
  margin-right: 6px;
}

.error-list,
.suggestion-list {
  margin: 0;
  padding-left: 20px;
}

.error-item,
.suggestion-item {
  margin: 4px 0;
  font-size: 12px;
  line-height: 1.4;
}

.validation-errors {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
}

.error-header,
.error-item {
  color: #f56c6c;
}

.validation-suggestions {
  background: #fdf6ec;
  border: 1px solid #f5dab1;
}

.suggestion-header,
.suggestion-item {
  color: #e6a23c;
}

/* 使用说明样式 */
.editor-guide-inline {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 12px 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  user-select: none;
  transition: all 0.3s ease;
  background: #f8f9fa;
  margin-bottom: 20px;
}

.editor-guide-inline .el-icon-info {
  margin-right: 8px;
  font-size: 16px;
  color: #D2691E;
}

.guide-title {
  flex: 1;
  font-weight: 500;
}

.guide-arrow {
  font-size: 14px;
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.guide-arrow.is-expanded {
  transform: rotate(180deg);
}

.editor-guide-inline.hover,
.editor-guide-inline:hover {
  background: #f5f0e8;
  color: #D2691E;
  border-color: #d4a574;
}

/* 使用说明内容样式 */
.guide-content {
  padding: 20px;
  color: #606266;
  font-size: 13px;
  line-height: 1.6;
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.guide-details h5 {
  color: #D2691E;
  font-size: 14px;
  font-weight: 600;
  margin: 16px 0 8px 0;
}

.guide-details h5:first-child {
  margin-top: 0;
}

.guide-details ul {
  margin: 8px 0 16px 0;
  padding-left: 20px;
}

.guide-details li {
  margin-bottom: 6px;
  line-height: 1.5;
}

.guide-details b {
  color: #D2691E;
  font-weight: 600;
}

.config-steps {
  background: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  margin: 16px 0;
}

.config-steps ol {
  margin: 0;
  padding-left: 20px;
}

.config-steps li {
  margin-bottom: 12px;
  font-weight: 500;
}

.config-steps ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.config-steps ul li {
  margin-bottom: 4px;
  font-weight: normal;
  color: #666;
}

.example-formula {
  background: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  margin-top: 16px;
}

.example-formula p {
  margin: 8px 0;
}

.example-formula ul,
.example-formula ol {
  margin: 8px 0 16px 20px;
}

.example-formula li {
  margin-bottom: 4px;
  color: #666;
}

/* 过渡动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import {
  getFormulaDetail,
  createFormula,
  updateFormula,
  validateFormula
} from '@/api/formulaEngine'
import {getEnterpriseTypeOptions} from '@/api/enterprise/type'
import {getDicItemList} from "@/config/tool";

export default {
  name: 'FormulaEdit',
  components: {
    EditPageContainer,
    UniversalForm
  },
  data() {
    return {
      loading: false,
      categoryOptions: [],
      enterpriseTypeOptions: [],
      showGuide: false,
      guideHover: false,
      validationResult: {
        valid: true,
        errors: [],
        suggestions: []
      },
      // 数学函数
      mathFunctions: [
        { name: 'pow', description: '幂函数 pow(x, y) - x的y次方' },
        { name: 'sqrt', description: '平方根函数 sqrt(x)' },
        { name: 'sin', description: '正弦函数 sin(x)' },
        { name: 'cos', description: '余弦函数 cos(x)' },
        { name: 'tan', description: '正切函数 tan(x)' },
        { name: 'log', description: '自然对数函数 log(x)' },
        { name: 'exp', description: '指数函数 exp(x)' },
        { name: 'abs', description: '绝对值函数 abs(x)' },
        { name: 'factorial', description: '阶乘函数 factorial(x)' }
      ],
      // 运算符
      operators: [
        { symbol: '+', description: '加法' },
        { symbol: '-', description: '减法' },
        { symbol: '*', description: '乘法' },
        { symbol: '/', description: '除法' },
        { symbol: '^', description: '幂运算' },
        { symbol: '(', description: '左括号' },
        { symbol: ')', description: '右括号' }
      ],
      // 常数
      constants: [
        { name: 'PI', description: '圆周率π ≈ 3.14159' },
        { name: 'E', description: '自然常数e ≈ 2.71828' },
        { name: 'A', description: '风险评分系数A' },
        { name: 'B', description: '风险评分系数B' },
        { name: 'C', description: '风险评分系数C' },
        { name: 'D', description: '风险评分系数D' }
      ],
      formData: {
        name: '',
        description: '',
        formula: '',
        variables: [],
        category: '',
        enterpriseType: [],
        status: 1,
        createTime: '',
        updateTime: '',
        createUser: '',
        updateUser: ''
      },
      formRules: {
        name: [
          { required: true, message: '请输入公式名称', trigger: 'blur' },
          { min: 1, max: 100, message: '公式名称长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入公式描述', trigger: 'blur' },
          { min: 1, max: 500, message: '公式描述长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        formula: [
          { required: true, message: '请输入公式内容', trigger: 'blur' },
          { min: 1, max: 1000, message: '公式内容长度在 1 到 1000 个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择公式分类', trigger: 'change' }
        ]
      },
      basicFormGroups: [
        {
          title: '基本信息',
          fields: [
            [{
              prop: 'name',
              label: '公式名称',
              type: 'input',
              placeholder: '请输入公式名称',
              required: true
            }],
            [{
              prop: 'description',
              label: '公式描述',
              type: 'textarea',
              placeholder: '请输入公式描述',
              required: true,
              rows: 3
            }],

            [{
              prop: 'category',
              label: '公式分类',
              type: 'select',
              placeholder: '请选择公式分类',
              required: true,
              options: []
            }],
            [{
              prop: 'enterpriseType',
              label: '适用企业类型',
              type: 'select',
              placeholder: '请选择适用的企业类型',
              multiple: true,
              options: []
            }],
            [{
              prop: 'status',
              label: '状态',
              type: 'radio',
              options: [
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ]
            }]
          ]
        },
        {
          title: '变量配置',
          fields: [
            [{
              prop: 'variables',
              label: '公式变量',
              type: 'list',
              addText: '添加变量',
              emptyText: '暂无变量，点击添加按钮添加变量',
              defaultRow: {
                name: '',
                type: 'number',
                defaultValue: '',
                description: ''
              },
              columns: [
                {
                  prop: 'name',
                  label: '变量名',
                  type: 'input',
                  placeholder: '请输入变量名',
                  required: true,
                  width: '120px'
                },
                {
                  prop: 'type',
                  label: '变量类型',
                  type: 'select',
                  placeholder: '请选择类型',
                  required: true,
                  width: '120px',
                  options: [
                    { label: '数值', value: 'number' },
                    { label: '变量', value: 'variable' },
                    { label: '字符串', value: 'string' }
                  ]
                },
                {
                  prop: 'defaultValue',
                  label: '默认值',
                  type: 'input',
                  placeholder: '请输入默认值',
                  width: '120px'
                },
                {
                  prop: 'description',
                  label: '变量描述',
                  type: 'input',
                  placeholder: '请输入变量描述',
                  width: '200px'
                }
              ],
              actions: [
                {
                  label: '删除',
                  type: 'danger',
                  icon: 'el-icon-delete'
                }
              ]
            }]
          ]
        }
      ]
    }
  },
  computed: {
    formulaId() {
      return this.$route.params.id
    },
    mode() {
      return this.$route.query.mode || 'add'
    },
    isView() {
      return this.mode === 'view'
    },
    isEdit() {
      return this.mode === 'edit'
    },
    isAdd() {
      return this.mode === 'add'
    },
    pageTitle() {
      if (this.isView) return '查看公式'
      if (this.isEdit) return '编辑公式'
      return '新增公式'
    },
    pageIcon() {
      return 'el-icon-data-analysis'
    },
    breadcrumbItems() {
      return [
        { text: '公式管理', to: { name: 'formulaEngineIndex' } },
        { text: this.pageTitle }
      ]
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      this.loading = true
      try {
        await Promise.all([
          this.loadCategoryOptions(),
          this.loadEnterpriseTypeOptions()
        ])

        if (this.formulaId) {
          await this.loadFormulaDetail()
        }
      } catch (error) {
        console.error('初始化失败:', error)
        this.$message.error('页面初始化失败')
      } finally {
        this.loading = false
      }
    },

    async loadCategoryOptions() {
      try {
        const response = await getDicItemList("elsm.formula.type")
        console.log(response)
        if (response) {
          this.categoryOptions = response.map(item => ({
            label: item.dicItemName,
            value: parseInt(item.dicItemCode)
          }))

          // 更新表单配置中的选项
          const categoryField = this.basicFormGroups[0].fields.flat().find(field => field.prop === 'category')
          if (categoryField) {
            categoryField.options = this.categoryOptions
          }
        }
      } catch (error) {
        console.error('加载分类选项失败:', error)
      }
    },

    async loadEnterpriseTypeOptions() {
      try {
        const response = await getEnterpriseTypeOptions()
        if (response) {
          // 处理不同的数据结构
          const data = response.list || response
          this.enterpriseTypeOptions = data.map(item => ({
            label: item.name,
            value: item.code
          }))

          // 更新表单配置中的选项
          const enterpriseTypeField = this.basicFormGroups[0].fields.flat().find(field => field.prop === 'enterpriseType')
          if (enterpriseTypeField) {
            enterpriseTypeField.options = this.enterpriseTypeOptions
          }
        }
      } catch (error) {
        console.error('加载企业类型选项失败:', error)
      }
    },

    async loadFormulaDetail() {
      try {
        const response = await getFormulaDetail(this.formulaId)
        if (response) {
          // 处理企业类型字段：优先使用后端返回的 enterpriseTypeList，如果没有则转换 enterpriseType 字符串
          if (response.enterpriseTypeList && Array.isArray(response.enterpriseTypeList)) {
            response.enterpriseType = response.enterpriseTypeList
          } else if (response.enterpriseType && typeof response.enterpriseType === 'string') {
            response.enterpriseType = response.enterpriseType.split(',').filter(type => type.trim())
          } else {
            response.enterpriseType = []
          }

          this.formData = {
            ...this.formData,
            ...response
          }
        } else {
          this.$message.error((response && response.message) || '加载公式详情失败')
          this.handleBack()
        }
      } catch (error) {
        console.error('加载公式详情失败:', error)
        this.$message.error('加载公式详情失败')
        this.handleBack()
      }
    },

    // 切换使用说明展开/收起
    toggleGuide() {
      this.showGuide = !this.showGuide
    },

    // 插入函数
    insertFunction(func) {
      const textarea = this.$refs.formulaInput.$el.querySelector('textarea')
      const cursorPos = textarea.selectionStart
      const formula = this.formData.formula || ''
      const beforeCursor = formula.substring(0, cursorPos)
      const afterCursor = formula.substring(cursorPos)

      this.formData.formula = beforeCursor + func.name + '()' + afterCursor

      // 设置光标位置到括号内
      this.$nextTick(() => {
        textarea.focus()
        textarea.setSelectionRange(cursorPos + func.name.length + 1, cursorPos + func.name.length + 1)
      })
    },

    // 插入运算符
    insertOperator(op) {
      const textarea = this.$refs.formulaInput.$el.querySelector('textarea')
      const cursorPos = textarea.selectionStart
      const formula = this.formData.formula || ''
      const beforeCursor = formula.substring(0, cursorPos)
      const afterCursor = formula.substring(cursorPos)

      this.formData.formula = beforeCursor + op.symbol + afterCursor

      // 设置光标位置到运算符后
      this.$nextTick(() => {
        textarea.focus()
        textarea.setSelectionRange(cursorPos + op.symbol.length, cursorPos + op.symbol.length)
      })
    },

    // 插入常数
    insertConstant(constant) {
      const textarea = this.$refs.formulaInput.$el.querySelector('textarea')
      const cursorPos = textarea.selectionStart
      const formula = this.formData.formula || ''
      const beforeCursor = formula.substring(0, cursorPos)
      const afterCursor = formula.substring(cursorPos)

      this.formData.formula = beforeCursor + constant.name + afterCursor

      // 设置光标位置到常数后
      this.$nextTick(() => {
        textarea.focus()
        textarea.setSelectionRange(cursorPos + constant.name.length, cursorPos + constant.name.length)
      })
    },

    // 插入变量
    insertVariable(variable) {
      const textarea = this.$refs.formulaInput.$el.querySelector('textarea')
      const cursorPos = textarea.selectionStart
      const formula = this.formData.formula || ''
      const beforeCursor = formula.substring(0, cursorPos)
      const afterCursor = formula.substring(cursorPos)

      this.formData.formula = beforeCursor + variable.name + afterCursor

      // 设置光标位置到变量后
      this.$nextTick(() => {
        textarea.focus()
        textarea.setSelectionRange(cursorPos + variable.name.length, cursorPos + variable.name.length)
      })
    },

    // 清空公式
    clearFormula() {
      this.$confirm('确定要清空公式内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formData.formula = ''
        this.validationResult = {
          valid: true,
          errors: [],
          suggestions: []
        }
        this.$message.success('公式已清空')
      }).catch(() => {
        // 用户取消
      })
    },

    // 公式内容变化时的处理
    onFormulaChange() {
      // 重置验证结果
      this.validationResult = {
        valid: true,
        errors: [],
        suggestions: []
      }
    },

    // 验证公式
    async validateFormula() {
      if (!this.formData.formula) {
        this.validationResult = {
          valid: false,
          errors: ['公式不能为空'],
          suggestions: []
        }
        this.$message.error('公式不能为空')
        return
      }

      try {
        const response = await validateFormula(this.formData.formula)
        if (response) {
          this.validationResult = {
            valid: true,
            errors: [],
            suggestions: []
          }
          this.$message.success('公式验证通过')
        } else {
          this.validationResult = {
            valid: false,
            errors: [response?.message || '公式验证失败'],
            suggestions: []
          }
          this.$message.error(response?.message || '公式验证失败')
        }
      } catch (error) {
        console.error('验证公式失败:', error)
        this.validationResult = {
          valid: false,
          errors: [error.message || '验证过程中出现错误'],
          suggestions: ['请检查公式语法是否正确']
        }
        this.$message.error('公式验证失败: ' + (error.message || '未知错误'))
      }
    },

    async handleSave() {
      try {
        // 验证基本信息表单
        const valid = await this.$refs.universalForm.validate()
        if (!valid) {
          this.$message.warning('请检查基本信息填写是否正确')
          return
        }

        // 验证公式表单
        if (this.$refs.formulaForm) {
          await this.$refs.formulaForm.validate()
        }

        // 验证公式语法
        if (!this.formData.formula) {
          this.$message.error('请输入公式内容')
          return
        }

        // 如果有验证结果且验证失败，提示用户
        if (this.validationResult && !this.validationResult.valid && this.validationResult.errors.length > 0) {
          this.$message.error('请先修复公式语法错误')
          return
        }

        this.loading = true

        // 转换数据格式以匹配后端期望
        const submitData = this.transformFormDataForSubmit(this.formData)

        let response

        if (this.isEdit) {
          response = await updateFormula(this.formulaId, submitData)
        } else {
          response = await createFormula(submitData)
        }

        if (response) {
          this.$message.success(this.isEdit ? '更新成功' : '创建成功')
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            this.handleBack()
          }, 1500)
        } else {
          this.$message.error((response && response.message) || (this.isEdit ? '更新失败' : '创建失败'))
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error(this.isEdit ? '更新失败' : '创建失败')
      } finally {
        this.loading = false
      }
    },

    handleBack() {
      const from = this.$route.query.from
      if (from === 'list') {
        this.$router.push({ name: 'formulaEngineIndex' })
      } else {
        this.$router.go(-1)
      }
    },

    handleBreadcrumbClick(breadcrumb) {
      if (breadcrumb.to) {
        this.$router.push(breadcrumb.to)
      }
    },

    // 转换表单数据为提交格式
    transformFormDataForSubmit(formData) {
      const submitData = { ...formData }

      // 验证必填字段
      if (!submitData.name || !submitData.name.trim()) {
        this.$message.error('请输入公式名称')
        throw new Error('公式名称不能为空')
      }

      if (!submitData.formula || !submitData.formula.trim()) {
        this.$message.error('请输入公式内容')
        throw new Error('公式内容不能为空')
      }

      if (!submitData.category) {
        this.$message.error('请选择公式分类')
        throw new Error('公式分类不能为空')
      }

      // 转换企业类型：数组转字符串
      if (Array.isArray(submitData.enterpriseType)) {
        submitData.enterpriseType = submitData.enterpriseType.length > 0
          ? submitData.enterpriseType.join(',')
          : null
      } else if (!submitData.enterpriseType) {
        submitData.enterpriseType = null
      }

      // 确保状态有默认值
      if (submitData.status === undefined || submitData.status === null) {
        submitData.status = 1
      }

      // 确保分类是数字类型
      if (typeof submitData.category === 'string') {
        submitData.category = parseInt(submitData.category)
      }

      // 移除前端特有的字段
      // delete submitData.createTime
      // delete submitData.updateTime
      // delete submitData.createUser
      // delete submitData.updateUser
      //
      // // 处理变量列表
      // if (submitData.variables && Array.isArray(submitData.variables)) {
      //   submitData.variables = submitData.variables.filter(v => v.name && v.name.trim())
      // } else {
      //   submitData.variables = []
      // }
      //
      // // 设置版本号
      // if (!submitData.version) {
      //   submitData.version = '1.0'
      // }

      return submitData
    }
  }
}
</script>

<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="true"
    :loading="loading"
    @back="handleBack"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <div class="formula-test-page">

    <div class="test-content" v-if="formulaData">
      <!-- 公式信息 -->
      <UniversalForm
        :form-data="formulaInfoData"
        :form-groups="formulaInfoGroups"
        :is-view="true"
        :label-width="'120px'"
      />

      <!-- 参数配置 -->
      <div class="parameter-section">
        <div class="section-header">
          <div class="header-left">
            <div class="section-icon">
              <i class="el-icon-data-line"></i>
            </div>
            <div class="section-title">
              <h4>参数配置</h4>
              <span class="section-subtitle">设置公式中的变量值</span>
            </div>
          </div>
          <div class="header-actions">
            <el-button
              type="primary"
              size="small"
              icon="el-icon-refresh"
              @click="resetToDefaults"
              class="reset-btn"
            >
              重置为默认值
            </el-button>
          </div>
        </div>

        <UniversalForm
          :form-data="testParams"
          :form-groups="parameterGroups"
          :is-view="false"
          :label-width="'120px'"
        />
      </div>

      <!-- 计算结果 -->
      <div class="result-section">
        <div class="section-header">
          <div class="header-left">
            <div class="section-icon">
              <i class="el-icon-data-analysis"></i>
            </div>
            <div class="section-title">
              <h4>计算结果</h4>
              <span class="section-subtitle">公式计算的实时结果</span>
            </div>
          </div>
          <div class="header-actions">
            <el-button
              type="primary"
              size="small"
              icon="el-icon-cpu"
              @click="calculateResult"
              :loading="calculating"
              class="calculate-btn"
            >
              重新计算
            </el-button>
          </div>
        </div>

        <div class="section-content">
          <div class="result-display">
            <div class="result-card" v-if="calculationResult">
              <div class="result-header">
                <div class="result-status">
                  <i class="el-icon-success" v-if="calculationResult.success"></i>
                  <i class="el-icon-error" v-else></i>
                  <span class="status-text">
                    {{ calculationResult.success ? '计算成功' : '计算失败' }}
                  </span>
                </div>
                <div class="result-time">
                  <i class="el-icon-time"></i>
                  {{ calculationResult.calculationTime }}
                </div>
              </div>

              <div class="result-content">
                <div class="result-value" v-if="calculationResult.success">
                  <span class="value-label">计算结果:</span>
                  <span class="value-number">{{ calculationResult.result }}</span>
                </div>

                <div class="result-error" v-else>
                  <span class="error-label">错误信息:</span>
                  <span class="error-message">{{ calculationResult.error }}</span>
                </div>
              </div>

              <div class="result-details">
                <div class="detail-item">
                  <span class="detail-label">使用公式:</span>
                  <div class="detail-formula">{{ calculationResult.formula }}</div>
                </div>

                <div class="detail-item">
                  <span class="detail-label">参数值:</span>
                  <div class="detail-params">
                    <span
                      v-for="(value, key) in calculationResult.variables"
                      :key="key"
                      class="param-tag"
                    >
                      {{ key }} = {{ value }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="no-result" v-else>
              <div class="no-result-icon">
                <i class="el-icon-data-analysis"></i>
              </div>
              <div class="no-result-text">
                <h3>暂无计算结果</h3>
                <p>请设置参数值后点击"重新计算"按钮</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 计算历史 -->
      <div class="history-section">
        <div class="section-header">
          <div class="header-left">
            <div class="section-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="section-title">
              <h4>计算历史</h4>
              <span class="section-subtitle">历史计算记录</span>
            </div>
          </div>
          <div class="header-actions">
            <el-button
              type="danger"
              size="small"
              icon="el-icon-delete"
              @click="clearHistory"
              class="clear-btn"
            >
              清空历史
            </el-button>
          </div>
        </div>

        <div class="section-content">
          <div class="history-list">
            <div
              v-for="(record, index) in calculationHistory"
              :key="index"
              class="history-item"
            >
              <div class="history-header">
                <div class="history-index">#{{ calculationHistory.length - index }}</div>
                <div class="history-time">{{ record.calculationTime }}</div>
                <div class="history-status">
                  <el-tag
                    :type="record.success ? 'success' : 'danger'"
                    size="mini"
                  >
                    {{ record.success ? '成功' : '失败' }}
                  </el-tag>
                </div>
              </div>

              <div class="history-content">
                <div class="history-params">
                  <span class="params-label">参数:</span>
                  <span
                    v-for="(value, key) in record.variables"
                    :key="key"
                    class="param-tag small"
                  >
                    {{ key }}={{ value }}
                  </span>
                </div>

                <div class="history-result">
                  <span class="result-label">结果:</span>
                  <span class="result-value" v-if="record.success">{{ record.result }}</span>
                  <span class="result-error" v-else>{{ record.error }}</span>
                </div>
              </div>
            </div>

            <div class="no-history" v-if="calculationHistory.length === 0">
              <div class="no-history-icon">
                <i class="el-icon-document"></i>
              </div>
              <div class="no-history-text">
                <p>暂无计算历史记录</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div class="error-container" v-else-if="!loading">
      <div class="error-content">
        <div class="error-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="error-text">
          <h3>加载失败</h3>
          <p>无法加载公式信息，请检查公式ID是否正确</p>
        </div>
        <div class="error-actions">
          <el-button type="primary" @click="loadFormulaData">重新加载</el-button>
          <el-button @click="handleBack">返回列表</el-button>
        </div>
      </div>
    </div>
    </div>
  </EditPageContainer>
</template>

<script>
import { getFormulaDetail, calculateFormula } from '@/api/formulaEngine';
import EditPageContainer from '@/components/layouts/EditPageContainer.vue';
import UniversalForm from '@/components/layouts/UniversalForm.vue';

export default {
  name: 'FormulaTest',
  components: {
    EditPageContainer,
    UniversalForm
  },
  data() {
    return {
      loading: false,
      calculating: false,
      formulaData: {
        id: null,
        name: '',
        category: '',
        content: '',
        description: '',
        variables: []
      },
      testParams: {},
      calculationResult: null,
      calculationHistory: [],
      // 面包屑导航
      breadcrumbItems: [
        { text: '首页', to: '/' },
        { text: '公式管理', to: '/formula-engine' },
        { text: '公式测试' }
      ]
    };
  },
  computed: {
    formulaId() {
      return this.$route.params.id
    },
    pageTitle() {
      return this.formulaData.name ? `测试公式: ${this.formulaData.name}` : '公式测试'
    },
    pageIcon() {
      return 'el-icon-cpu'
    },
    // 公式信息表单数据
    formulaInfoData() {
      return {
        name: this.formulaData.name || '',
        category: this.formulaData.category || '',
        content: this.formulaData.content || '',
        description: this.formulaData.description || ''
      }
    },
    // 公式信息表单配置
    formulaInfoGroups() {
      return [
        [
          {
            prop: 'name',
            label: '公式名称',
            type: 'input',
            span: 12
          },
          {
            prop: 'category',
            label: '公式分类',
            type: 'tag',
            span: 12,
            tagType: this.getCategoryTagType(this.formulaData.category)
          }
        ],
        [
          {
            prop: 'content',
            label: '公式内容',
            type: 'textarea',
            span: 24,
            rows: 3
          }
        ],
        [
          {
            prop: 'description',
            label: '公式描述',
            type: 'textarea',
            span: 24,
            rows: 2
          }
        ]
      ]
    },
    // 参数配置表单配置
    parameterGroups() {
      if (!this.formulaData.variables || this.formulaData.variables.length === 0) {
        return []
      }

      const groups = []
      const variables = this.formulaData.variables

      // 每行显示2个参数
      for (let i = 0; i < variables.length; i += 2) {
        const row = []

        // 第一个参数
        if (variables[i]) {
          row.push({
            prop: variables[i].name,
            label: variables[i].name,
            type: 'input-number',
            span: 12,
            precision: 4,
            step: this.getStep(variables[i].type),
            placeholder: `请输入${variables[i].name}的值`,
            suffix: variables[i].description ? `(${variables[i].description})` : ''
          })
        }

        // 第二个参数
        if (variables[i + 1]) {
          row.push({
            prop: variables[i + 1].name,
            label: variables[i + 1].name,
            type: 'input-number',
            span: 12,
            precision: 4,
            step: this.getStep(variables[i + 1].type),
            placeholder: `请输入${variables[i + 1].name}的值`,
            suffix: variables[i + 1].description ? `(${variables[i + 1].description})` : ''
          })
        }

        groups.push(row)
      }

      return groups
    }
  },
  mounted() {
    this.loadFormulaData();
  },
  methods: {
    // 加载公式数据
    async loadFormulaData() {
      if (!this.formulaId) {
        this.$message.error('缺少公式ID');
        return;
      }

      this.loading = true;
      try {
        const response = await getFormulaDetail(this.formulaId);
        if (response.code === 200) {
          this.formulaData = response.data;
          this.initializeParams();
          this.calculateResult(); // 自动计算一次
        } else {
          this.$message.error(response.message || '加载公式失败');
        }
      } catch (error) {
        this.$message.error('加载公式失败');
        console.error('加载公式失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 初始化参数
    initializeParams() {
      this.testParams = {};
      if (this.formulaData && this.formulaData.variables) {
        this.formulaData.variables.forEach(variable => {
          this.testParams[variable.name] = variable.defaultValue || 0;
        });
      }
    },

    // 重置为默认值
    resetToDefaults() {
      this.initializeParams();
      this.calculateResult();
    },



    // 计算结果
    async calculateResult() {
      if (!this.formulaData || !this.testParams) {
        return;
      }

      this.calculating = true;
      try {
        const response = await calculateFormula(this.formulaId, this.testParams);

        if (response.code === 200) {
          this.calculationResult = {
            success: true,
            result: response.data.result,
            formula: response.data.formula,
            variables: response.data.variables,
            calculationTime: response.data.calculationTime
          };
        } else {
          this.calculationResult = {
            success: false,
            error: response.message || '计算失败',
            formula: this.formulaData.formula,
            variables: this.testParams,
            calculationTime: new Date().toLocaleString()
          };
        }

        // 添加到历史记录
        this.calculationHistory.push({ ...this.calculationResult });

        // 限制历史记录数量
        if (this.calculationHistory.length > 10) {
          this.calculationHistory.shift();
        }

      } catch (error) {
        this.calculationResult = {
          success: false,
          error: '计算请求失败',
          formula: this.formulaData.formula,
          variables: this.testParams,
          calculationTime: new Date().toLocaleString()
        };
        console.error('计算失败:', error);
      } finally {
        this.calculating = false;
      }
    },

    // 清空历史
    clearHistory() {
      this.$confirm('确定要清空所有计算历史记录吗？', '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.calculationHistory = [];
        this.$message.success('历史记录已清空');
      });
    },

    // 返回列表
    handleBack() {
      this.$router.push({ name: 'formulaEngineIndex' });
    },

    // 面包屑点击处理
    handleBreadcrumbClick(item) {
      if (item.to) {
        this.$router.push(item.to);
      }
    },

    // 获取步长
    getStep(type) {
      return type === 'number' ? 0.1 : 1;
    },

    // 获取分类标签类型
    getCategoryTagType(category) {
      const typeMap = {
        '数学计算': 'primary',
        '风险评估': 'warning',
        '收益计算': 'success',
        '统计分析': 'info'
      };
      return typeMap[category] || 'default';
    }
  }
};
</script>

<style lang="less" scoped>
@import '../../assets/style/shared-styles.less';

.formula-test-page {
  min-height: 100vh;
  background: #fbf6ee;

  // 公式信息区域
  .formula-info-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #f7ecdd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 20px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #fbf6ee;
      border-bottom: 1px solid #f7ecdd;
      border-radius: 12px 12px 0 0;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .section-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(215, 162, 86, 0.1);

          i {
            font-size: 18px;
            color: #D7A256;
          }
        }

        .section-title {
          h4 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
          }

          .section-subtitle {
            font-size: 13px;
            color: #909399;
            font-weight: 400;
          }
        }
      }
    }
  }

  // 参数配置区域
  .parameter-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #f7ecdd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 20px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #fbf6ee;
      border-bottom: 1px solid #f7ecdd;
      border-radius: 12px 12px 0 0;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .section-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(215, 162, 86, 0.1);

          i {
            font-size: 18px;
            color: #D7A256;
          }
        }

        .section-title {
          h4 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
          }

          .section-subtitle {
            font-size: 13px;
            color: #909399;
            font-weight: 400;
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;

        .action-btn {
          height: 32px;
          padding: 0 16px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 14px;
          transition: all 0.3s ease;
          border: 1px solid #D7A256;

          &.calculate-btn {
            background: #D7A256;
            color: white;

            &:hover {
              background: #E6B366;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(215, 162, 86, 0.3);
            }

            &:loading {
              opacity: 0.7;
            }
          }

          &.reset-btn {
            background: white;
            color: #D7A256;

            &:hover {
              background: #D7A256;
              color: white;
              transform: translateY(-1px);
            }
          }

          i {
            margin-right: 4px;
          }
        }
      }
    }
  }

  // 计算结果区域
  .result-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #f7ecdd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 20px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #fbf6ee;
      border-bottom: 1px solid #f7ecdd;
      border-radius: 12px 12px 0 0;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .section-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(215, 162, 86, 0.1);

          i {
            font-size: 18px;
            color: #D7A256;
          }
        }

        .section-title {
          h4 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
          }

          .section-subtitle {
            font-size: 13px;
            color: #909399;
            font-weight: 400;
          }
        }
      }
    }

    .section-content {
      padding: 20px;

      .result-display {
        .result-item {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          background: #f8f9fa;
          border-radius: 8px;
          margin-bottom: 12px;
          border-left: 4px solid #D7A256;

          &:last-child {
            margin-bottom: 0;
          }

          &.success {
            background: #f0f9ff;
            border-left-color: #67c23a;

            .result-icon {
              color: #67c23a;
            }
          }

          &.error {
            background: #fef0f0;
            border-left-color: #f56c6c;

            .result-icon {
              color: #f56c6c;
            }
          }

          .result-icon {
            font-size: 18px;
            margin-right: 12px;
            color: #D7A256;
          }

          .result-content {
            flex: 1;

            .result-label {
              font-size: 14px;
              color: #606266;
              margin-bottom: 4px;
            }

            .result-value {
              font-size: 16px;
              font-weight: 600;
              color: #2c3e50;
              font-family: 'Consolas', 'Monaco', monospace;
            }
          }
        }

        .no-result {
          text-align: center;
          padding: 40px 20px;
          color: #909399;
          font-size: 14px;

          i {
            font-size: 48px;
            color: #dcdfe6;
            margin-bottom: 16px;
            display: block;
          }
        }
      }
    }
  }

  // 历史记录区域
  .history-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #f7ecdd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #fbf6ee;
      border-bottom: 1px solid #f7ecdd;
      border-radius: 12px 12px 0 0;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .section-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(215, 162, 86, 0.1);

          i {
            font-size: 18px;
            color: #D7A256;
          }
        }

        .section-title {
          h4 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
          }

          .section-subtitle {
            font-size: 13px;
            color: #909399;
            font-weight: 400;
          }
        }
      }

      .header-actions {
        .clear-btn {
          height: 32px;
          padding: 0 16px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 14px;
          transition: all 0.3s ease;
          background: white;
          color: #f56c6c;
          border: 1px solid #f56c6c;

          &:hover {
            background: #f56c6c;
            color: white;
            transform: translateY(-1px);
          }

          i {
            margin-right: 4px;
          }
        }
      }
    }

    .section-content {
      padding: 20px;

      .history-list {
        .history-item {
          padding: 16px;
          background: #f8f9fa;
          border-radius: 8px;
          margin-bottom: 12px;
          border-left: 4px solid #D7A256;
          transition: all 0.3s ease;

          &:last-child {
            margin-bottom: 0;
          }

          &:hover {
            background: #f0f9ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          &.success {
            border-left-color: #67c23a;
          }

          &.error {
            border-left-color: #f56c6c;
          }

          .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .status-tag {
              font-size: 12px;
              padding: 2px 8px;
              border-radius: 4px;
              font-weight: 500;

              &.success {
                background: rgba(103, 194, 58, 0.1);
                color: #67c23a;
              }

              &.error {
                background: rgba(245, 108, 108, 0.1);
                color: #f56c6c;
              }
            }

            .calculation-time {
              font-size: 12px;
              color: #909399;
            }
          }

          .history-content {
            .result-value {
              font-size: 16px;
              font-weight: 600;
              color: #2c3e50;
              font-family: 'Consolas', 'Monaco', monospace;
              margin-bottom: 8px;
            }

            .variables-info {
              font-size: 12px;
              color: #606266;
              background: white;
              padding: 8px 12px;
              border-radius: 4px;
              font-family: 'Consolas', 'Monaco', monospace;
            }
          }
        }

        .no-history {
          text-align: center;
          padding: 40px 20px;
          color: #909399;
          font-size: 14px;

          i {
            font-size: 48px;
            color: #dcdfe6;
            margin-bottom: 16px;
            display: block;
          }
        }
      }
    }
  }

  // UniversalForm 组件样式优化
  /deep/ .universal-form-container {
    padding: 0;

    .universal-form {
      .form-group {
        margin-bottom: 0;
      }

      .group-title {
        display: none; // 隐藏UniversalForm自带的标题，使用外层section-header
      }

      .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
          font-weight: 500;
          color: #2c3e50;
          font-size: 14px;
        }

        .el-input,
        .el-select,
        .el-input-number {
          .el-input__inner {
            border-radius: 8px;
            border-color: #dcdfe6;
            font-size: 14px;
            transition: all 0.3s ease;

            &:focus {
              border-color: #D7A256;
              box-shadow: 0 0 0 2px rgba(215, 162, 86, 0.1);
            }

            &:hover {
              border-color: #c0c4cc;
            }
          }
        }

        .el-input-number {
          width: 100%;
          max-width: 300px;

          .el-input__inner {
            text-align: left;
          }
        }

        .view-value {
          padding: 8px 12px;
          background: #f5f7fa;
          border-radius: 8px;
          color: #606266;
          font-size: 14px;
          line-height: 1.4;
          min-height: 20px;
          border: 1px solid #dcdfe6;

          &.description-view {
            white-space: pre-wrap;
            word-break: break-word;
            min-height: 60px;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .formula-test-page {
      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .header-actions {
          width: 100%;
          justify-content: flex-end;
        }
      }

      .parameter-section {
        .header-actions {
          flex-direction: column;
          gap: 8px;

          .action-btn {
            width: 100%;
          }
        }
      }

      .result-section,
      .history-section {
        .section-content {
          padding: 16px;
        }
      }
    }

    /deep/ .universal-form-container {
      .universal-form {
        .el-form-item {
          .el-input-number {
            max-width: 100%;
          }
        }
      }
    }
  }
}
</style>

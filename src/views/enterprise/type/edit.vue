<template>
  <EditPageContainer
      :title="pageTitle"
      :icon="pageIcon"
      :breadcrumb-items="breadcrumbItems"
    :loading="loading"
      @back="handleBack"
    @save="handleSave"
      @breadcrumb-click="handleBreadcrumbClick"
    >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'100px'"
      :rowGutter="20"
      hide-required-asterisk
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getEnterpriseTypeDetail, createEnterpriseType, updateEnterpriseType } from '@/api/enterprise/type.js'

export default {
  name: 'EnterpriseTypeEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      isEdit: false,
      loading: false,
      form: {
        name: '',
        code: '',
        description: '',
        priority: 1,
        rules: [
          { field: 'employeeCount', minValue: null, maxValue: null },
          { field: 'revenue', minValue: null, maxValue: null }
        ]
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'name', label: '类型名称', type: 'input', placeholder: '请输入企业类型名称', maxlength: 50, showWordLimit: true },
              { prop: 'code', label: '类型编码', type: 'input', placeholder: '请输入类型编码，如：SMALL', maxlength: 20 },
              { prop: 'priority', label: '优先级', type: 'number', placeholder: '请设置优先级', min: 1, max: 10, step: 1 }
            ],
            [
              { prop: 'description', label: '类型描述', type: 'textarea', placeholder: '请输入类型描述', maxlength: 200, rows: 3, showWordLimit: true, span: 24 }
            ]
          ]
        },
        {
          title: '判定规则',
          icon: 'el-icon-s-operation',
          fields: [
            [
              {
                type: 'list',
                prop: 'rules',
                label: '判定规则',
                icon: 'el-icon-s-operation',
                min: 1,
                defaultRow: () => ({ field: 'employeeCount', minValue: null, maxValue: null }),
                columns: [
                  {
                    prop: 'field',
                    label: '字段',
                    type: 'select',
                    width: 150,
                    disabled: true,
                    options: [
                      { label: '人员规模', value: 'employeeCount' },
                      { label: '营收规模', value: 'revenue' }
                    ]
                  },
                  {
                    prop: 'minValue',
                    label: '最小值',
                    type: 'number',
                  },
                  {
                    prop: 'maxValue',
                    label: '最大值',
                    type: 'number',
                  }
                ]
              }
            ]
          ]
        }
      ],
      formRules: {
        name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入类型编码', trigger: 'blur' }],
        priority: [{ required: true, message: '请设置优先级', trigger: 'blur' }]
      }
    }
  },
  computed: {
    pageTitle() {
      return this.isEdit ? '编辑企业类型' : '新建企业类型'
    },
    pageIcon() {
      return 'el-icon-s-data'
    },
    breadcrumbItems() {
      return [
        { text: '企业类型管理', to: { name: 'enterpriseType' }, icon: 'el-icon-s-data' },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    }
  },
  created() {
    const id = this.$route.params.id
    if (id) {
      this.isEdit = true
      this.loadData(id)
    }
  },
  methods: {
    async loadData(id) {
      try {
        const response = await getEnterpriseTypeDetail(id)
        // 后端直接返回数据，不需要判断code
        this.form = {
          name: response.name,
          code: response.code,
          description: response.description,
          priority: response.priority,
          rules: [
            { field: 'employeeCount', minValue: null, maxValue: null },
            { field: 'revenue', minValue: null, maxValue: null }
          ]
        }

        // 解析后端返回的rules数据
        if (response.rules && response.rules.length > 0) {
          response.rules.forEach(rule => {
            if (rule.field === 'employeeCount') {
              this.form.rules[0].minValue = rule.minValue
              this.form.rules[0].maxValue = rule.maxValue
            } else if (rule.field === 'revenue') {
              this.form.rules[1].minValue = rule.minValue
              this.form.rules[1].maxValue = rule.maxValue
            }
          })
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      }
    },
    async handleSave() {
      await this.$refs.universalForm.validate()

      // 过滤出有效的规则（至少有最小值或最大值）
      const validRules = this.form.rules.filter(rule =>
        rule.minValue !== null && rule.minValue !== '' ||
        rule.maxValue !== null && rule.maxValue !== ''
      )

      if (validRules.length === 0) {
        this.$message.warning('请至少设置一个规模范围')
        return
      }

      this.loading = true
      try {
        const saveData = {
          name: this.form.name,
          code: this.form.code,
          description: this.form.description,
          priority: this.form.priority,
          rules: validRules
        }

        if (this.isEdit) {
          // 更新时需要传入id
          saveData.id = this.$route.params.id
          await updateEnterpriseType(saveData)
        } else {
          await createEnterpriseType(saveData)
        }
        this.$message.success(this.isEdit ? '更新成功' : '创建成功')
        this.$router.back()
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleBack() {
      this.$router.back()
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    }
  }
}
</script>

<template>
  <div class="enterprise-info-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="企业信息管理"
      subtitle="管理企业基本信息，支持企业档案维护和分类管理"
      title-icon="el-icon-office-building"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="220"
      :search-label-width="'100px'"
      :show-add-button="false"
      empty-title="暂无企业数据"
      empty-description="点击上方新增企业按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
      >
      <!-- 自定义列内容插槽 -->
      <template #name="{ row }">
            <div class="name-cell">
              <div class="name-icon">
                <i class="el-icon-office-building"></i>
              </div>
              <div class="name-content">
            <div class="name-text">{{ row.name }}</div>
            <div class="credit-code" v-if="row.creditCode">
              {{ row.creditCode }}
                </div>
              </div>
            </div>
          </template>

      <template #industry="{ row }">
        <div class="industry-cell">
          {{ row.industry || '-' }}
        </div>
      </template>

      <template #regStatus="{ row }">
        <el-tag :type="getStatusTagType(row.regStatus)" size="small">
          {{ row.regStatus || '未知' }}
        </el-tag>
      </template>

      <template #companyOrgType="{ row }">
        <div class="company-type-cell">
          {{ row.scale || '-' }}
        </div>
      </template>

      <template #legalPersonName="{ row }">
        <div class="legal-person-cell">
          {{ row.legalPersonName || '-' }}
        </div>
      </template>

      <template #regCapital="{ row }">
        <div class="capital-cell">
          {{ formatCapital(row.regCapital) }}
        </div>
      </template>

      <template #staffNumRange="{ row }">
        <div class="staff-cell">
          {{ row.staffNumRange || '-' }}
        </div>
      </template>

      <template #phoneNumber="{ row }">
        <div class="phone-cell">
          {{ row.phoneNumber || '-' }}
        </div>
      </template>

      <template #regLocation="{ row }">
        <el-tooltip
          :content="row.regLocation"
          placement="top"
          :disabled="!row.regLocation || row.regLocation.length <= 20"
        >
          <div class="address-text">
            {{ getShortAddress(row.regLocation) }}
          </div>
        </el-tooltip>
      </template>

      <template #establishTime="{ row }">
        <div class="time-cell">
          {{ formatTimestamp(row.establishTime) }}
        </div>
      </template>
    </UniversalTable>
  </div>
</template>

<script>
import { getEnterpriseList, deleteEnterprise } from '@/api/enterprise/index.js'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'EnterpriseInfoList',
  components: {
    UniversalTable,
    ConfirmDialog
  },

  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        param: {
          name: '',
          creditCode: '',
          regStatus: '',
          industry: ''
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 表格列配置
      tableColumns: [
        {
          prop: 'name',
          label: '企业名称',
          minWidth: 150,
          align: 'center'
        },
        {
          prop: 'regStatus',
          label: '企业状态',
          width: 80,
          align: 'center'
        },
        {
          prop: 'industry',
          label: '所属行业',
          width: 120,
          align: 'center'
        },
        {
          prop: 'companyOrgType',
          label: '企业类型',
          width: 120,
          align: 'center'
        },
        {
          prop: 'legalPersonName',
          label: '法定代表人',
          width: 100,
          align: 'center'
        },
        {
          prop: 'regCapital',
          label: '注册资本',
          width: 160,
          align: 'center'
        },
        {
          prop: 'staffNumRange',
          label: '人员规模',
          width: 100,
          align: 'center'
        },
        {
          prop: 'phoneNumber',
          label: '联系电话',
          width: 120,
          align: 'center'
        },
        {
          prop: 'regLocation',
          label: '注册地址',
          width: 180,
          align: 'center'
        },
        {
          prop: 'establishTime',
          label: '成立时间',
          width: 120,
          align: 'center'
        }
      ],
      // 操作按钮配置
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        },
        {
          key: 'kycQuery',
          label: 'KYC',
          icon: 'el-icon-user',
          class: 'kyc-btn',
          size: 'mini'
        },
        {
          key: 'complianceQuery',
          label: '合规',
          icon: 'el-icon-s-check',
          class: 'compliance-btn',
          size: 'mini'
        }
      ],
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '企业名称',
          name: 'name',
          type: 'input',
          placeholder: '请输入企业名称'
        },
        {
          label: '信用代码',
          name: 'creditCode',
          type: 'input',
          placeholder: '请输入统一社会信用代码'
        },
        {
          label: '企业状态',
          name: 'regStatus',
          type: 'select',
          placeholder: '请选择企业状态',
          list: [
            { dicItemCode: '存续', dicItemName: '存续' },
            { dicItemCode: '在业', dicItemName: '在业' },
            { dicItemCode: '注销', dicItemName: '注销' },
            { dicItemCode: '吊销', dicItemName: '吊销' },
            { dicItemCode: '迁出', dicItemName: '迁出' }
          ]
        },
        {
          label: '所属行业',
          name: 'industry',
          type: 'input',
          placeholder: '请输入所属行业'
        }
      ],
      currentRow: null
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const data = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: {
            name: this.searchForm.param.name || null,
            creditCode: this.searchForm.param.creditCode || null,
            regStatus: this.searchForm.param.regStatus || null,
            industry: this.searchForm.param.industry || null
          }
        }

        const response = await getEnterpriseList(data)
        // 后端返回的数据格式：response.list, response.total
        this.tableData = response.list || []
        this.pagination.total = response.total || 0
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 处理操作按钮点击
    handleAction({ action, row }) {
      this.currentRow = row

      switch (action) {
        case 'view':
          this.handleView(row)
          break
        case 'kycQuery':
          this.handleKYCQuery(row)
          break
        case 'complianceQuery':
          this.handleComplianceQuery(row)
          break
      }
    },

    handleSearch(searchData) {
      this.searchForm.param = { ...searchData.param }
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleReset() {
      this.searchForm.param = {
        name: '',
        creditCode: '',
        regStatus: '',
        industry: ''
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.loadData()
    },

    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },

    // 格式化注册资本
    formatCapital(capital) {
      if (!capital) return '-'
      return `${capital}`
    },

    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleDateString()
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '存续': 'success',
        '在业': 'success',
        '注销': 'info',
        '吊销': 'danger',
        '迁出': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // 获取短地址
    getShortAddress(address) {
      if (!address) return '-'
      return address.length > 20 ? address.substring(0, 20) + '...' : address
    },

    handleView(row) {
      this.$router.push({
        name: 'thirdPartyQuery',
        params: { id: row.id, enterpriseName: row.name }
      })
    },

    handleKYCQuery(row) {
      this.$router.push({
        name: 'kycQuery',
        params: { id: row.id, enterpriseName: row.name }
      })
    },

    handleComplianceQuery(row) {
      this.$message.info(`正在查询企业「${row.name}」的合规审核记录...`)
      // TODO: 实现合规审核记录查询功能
      // this.$router.push({ name: 'complianceQuery', params: { id: row.id } })
    }
  }
}
</script>

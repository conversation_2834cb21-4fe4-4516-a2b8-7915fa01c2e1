<template>
  <div class="enterprise-query-config-container">
    <!-- 通用规则提示 -->
    <div class="general-config-section">
      <div class="config-card">
        <div class="config-header">
          <div class="config-title">
            <i class="el-icon-setting"></i>
            <span>通用规则配置</span>
          </div>
          <el-button
            type="primary"
            size="small"
            @click="handleEditGeneralConfig"
            icon="el-icon-edit"
          >
            编辑
          </el-button>
        </div>
        <div class="config-content">
          <div class="config-item">
            <span class="config-label">每天限制：</span>
            <span class="config-value">{{ generalConfig.dailyLimit }}次</span>
          </div>
          <div class="config-item">
            <span class="config-label">每月限制：</span>
            <span class="config-value">{{ generalConfig.monthlyLimit }}次</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="企业查询配置"
      subtitle="管理顾问查询限制配置，支持个人特殊配置和使用情况统计"
      title-icon="el-icon-s-data"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :search-label-width="'100px'"
      :show-add-button="false"
      empty-title="暂无查询配置数据"
      empty-description="暂无符合条件的查询配置信息"
      @search="handleSearch"
      @reset="handleReset"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
    </UniversalTable>

    <!-- 通用配置编辑对话框 -->
    <el-dialog
      title="编辑通用规则配置"
      :visible.sync="generalConfigDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="generalConfigForm"
        :model="generalConfigForm"
        :rules="generalConfigRules"
        label-width="120px"
      >
        <el-form-item label="每日限制" prop="dailyLimit">
          <el-input-number
            v-model="generalConfigForm.dailyLimit"
            :min="1"
            :max="999"
            placeholder="请输入每日查询次数限制"
          />
          <span class="form-tip">次</span>
        </el-form-item>
        <el-form-item label="每月限制" prop="monthlyLimit">
          <el-input-number
            v-model="generalConfigForm.monthlyLimit"
            :min="1"
            :max="9999"
            placeholder="请输入每月查询次数限制"
          />
          <span class="form-tip">次</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="generalConfigDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSaveGeneralConfig"
          :loading="generalConfigSaving"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <!-- 个人配置设置对话框 -->
    <el-dialog
      title="设置个人查询限制"
      :visible.sync="personalConfigDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="personalConfigForm"
        :model="personalConfigForm"
        :rules="personalConfigRules"
        label-width="120px"
      >
        <el-form-item label="顾问姓名">
          <el-input v-model="personalConfigForm.agentName" disabled />
        </el-form-item>
        <el-form-item label="顾问工号">
          <el-input v-model="personalConfigForm.agentCode" disabled />
        </el-form-item>
        <el-form-item label="每日限制" prop="dailyLimit">
          <el-input-number
            v-model="personalConfigForm.dailyLimit"
            :min="1"
            :max="999"
            placeholder="请输入每日查询次数限制"
          />
          <span class="form-tip">次</span>
        </el-form-item>
        <el-form-item label="每月限制" prop="monthlyLimit">
          <el-input-number
            v-model="personalConfigForm.monthlyLimit"
            :min="1"
            :max="9999"
            placeholder="请输入每月查询次数限制"
          />
          <span class="form-tip">次</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="personalConfigDialogVisible = false">取消</el-button>
        <el-button
          @click="handleRemovePersonalConfig"
          :loading="personalConfigSaving"
          v-if="currentRow && currentRow.isSpecialConfig"
        >
          恢复默认
        </el-button>
        <el-button
          type="primary"
          @click="handleSavePersonalConfig"
          :loading="personalConfigSaving"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable'
import {
  getQueryUsageStatistics,
  getGeneralConfig,
  updateGeneralConfig,
  setPersonalConfig,
  removePersonalConfig
} from '@/api/enterprise/query/config'

export default {
  name: 'EnterpriseQueryConfig',
  components: {
    UniversalTable
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        param: {
          agentName: '',
          agentCode: ''
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },

      // 通用配置
      generalConfig: {
        dailyLimit: 10,
        monthlyLimit: 30
      },

      // 通用配置对话框
      generalConfigDialogVisible: false,
      generalConfigSaving: false,
      generalConfigForm: {
        dailyLimit: 10,
        monthlyLimit: 30
      },
      generalConfigRules: {
        dailyLimit: [
          { required: true, message: '请输入每日查询次数限制', trigger: 'blur' },
          { type: 'number', min: 1, max: 999, message: '每日限制必须在1-999之间', trigger: 'blur' }
        ],
        monthlyLimit: [
          { required: true, message: '请输入每月查询次数限制', trigger: 'blur' },
          { type: 'number', min: 1, max: 9999, message: '每月限制必须在1-9999之间', trigger: 'blur' }
        ]
      },

      // 个人配置对话框
      personalConfigDialogVisible: false,
      personalConfigSaving: false,
      personalConfigForm: {
        agentCode: '',
        agentName: '',
        dailyLimit: 10,
        monthlyLimit: 30
      },
      personalConfigRules: {
        dailyLimit: [
          { required: true, message: '请输入每日查询次数限制', trigger: 'blur' },
          { type: 'number', min: 1, max: 999, message: '每日限制必须在1-999之间', trigger: 'blur' }
        ],
        monthlyLimit: [
          { required: true, message: '请输入每月查询次数限制', trigger: 'blur' },
          { type: 'number', min: 1, max: 9999, message: '每月限制必须在1-9999之间', trigger: 'blur' }
        ]
      },

      // 当前操作行
      currentRow: null,

      // 搜索表单配置
      searchFormConfig: [
        {
          label: '顾问姓名',
          name: 'agentName',
          type: 'input',
          placeholder: '请输入顾问姓名'
        },
        {
          label: '顾问工号',
          name: 'agentCode',
          type: 'input',
          placeholder: '请输入顾问工号'
        }
      ],

      // 表格列配置
      tableColumns: [
        {
          prop: 'agentName',
          label: '姓名',
          width: 100,
          align: 'center'
        },
        {
          prop: 'agentCode',
          label: '工号',
          width: 120,
          align: 'center'
        },
        {
          prop: 'legalName',
          label: '机构',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'salesCenterName',
          label: '营业部',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'todayUsage',
          label: '当天使用次数',
          width: 120,
          align: 'center',
          formatter: (value, row, column) => {
            return `${value || 0}/${row.dailyLimit || 0}`
          }
        },
        {
          prop: 'monthUsage',
          label: '本月使用次数',
          width: 120,
          align: 'center',
          formatter: (value, row, column) => {
            return `${value || 0}/${row.monthlyLimit || 0}`
          }
        },
        {
          prop: 'isSpecialConfig',
          label: '是否特殊配置',
          width: 120,
          align: 'center',
          formatter: (value, row, column) => {
            return value ? '是' : '否'
          }
        }
      ],

      // 操作按钮配置
      tableActions: [
        {
          label: '设置',
          type: 'primary',
          size: 'small',
          key: 'setting'
        }
      ]
    }
  },

  mounted() {
    this.loadGeneralConfig()
    this.loadData()
  },

  methods: {
    // 加载通用配置
    async loadGeneralConfig() {
      try {
        const response = await getGeneralConfig()
        if (response) {
          this.generalConfig = response
        }
      } catch (error) {
        console.error('加载通用配置失败:', error)
      }
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: {
            ...this.searchForm.param
          }
        }

        const response = await getQueryUsageStatistics(params)
        if (response && response.list) {
          this.tableData = response.list || []
          this.pagination.total = response.total || 0
        }
      } catch (error) {
        console.error('加载查询配置数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch(searchData) {
      this.searchForm.param = { ...searchData.param }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm.param = {
        agentName: '',
        agentCode: ''
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },

    // 操作按钮点击
    handleAction(payload) {
      const { action, row } = payload
      if (action === 'setting') {
        this.handleSetPersonalConfig(row)
      }
    },

    // 编辑通用配置
    handleEditGeneralConfig() {
      this.generalConfigForm = { ...this.generalConfig }
      this.generalConfigDialogVisible = true
    },

    // 保存通用配置
    async handleSaveGeneralConfig() {
      this.$refs.generalConfigForm.validate(async (valid) => {
        if (valid) {
          this.generalConfigSaving = true
          try {
            const data = {
              configType: 'GENERAL',
              dailyLimit: this.generalConfigForm.dailyLimit,
              monthlyLimit: this.generalConfigForm.monthlyLimit
            }
            await updateGeneralConfig(data)
            this.$message.success('保存成功')
            this.generalConfigDialogVisible = false
            this.loadGeneralConfig()
            this.loadData() // 重新加载数据以更新限制显示
          } catch (error) {
            console.error('保存通用配置失败:', error)
            this.$message.error('保存失败')
          } finally {
            this.generalConfigSaving = false
          }
        }
      })
    },

    // 设置个人配置
    handleSetPersonalConfig(row) {
      this.currentRow = row
      this.personalConfigForm = {
        agentCode: row.agentCode,
        agentName: row.agentName,
        dailyLimit: row.dailyLimit,
        monthlyLimit: row.monthlyLimit
      }
      this.personalConfigDialogVisible = true
    },

    // 保存个人配置
    async handleSavePersonalConfig() {
      this.$refs.personalConfigForm.validate(async (valid) => {
        if (valid) {
          this.personalConfigSaving = true
          try {
            const data = {
              configType: 'PERSONAL',
              agentCode: this.personalConfigForm.agentCode,
              agentName: this.personalConfigForm.agentName,
              dailyLimit: this.personalConfigForm.dailyLimit,
              monthlyLimit: this.personalConfigForm.monthlyLimit
            }
            await setPersonalConfig(data)
            this.$message.success('设置成功')
            this.personalConfigDialogVisible = false
            this.loadData()
          } catch (error) {
            console.error('设置个人配置失败:', error)
            this.$message.error('设置失败')
          } finally {
            this.personalConfigSaving = false
          }
        }
      })
    },

    // 删除个人配置
    async handleRemovePersonalConfig() {
      this.$confirm('确定要恢复为默认配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.personalConfigSaving = true
        try {
          await removePersonalConfig(this.personalConfigForm.agentCode)
          this.$message.success('恢复默认成功')
          this.personalConfigDialogVisible = false
          this.loadData()
        } catch (error) {
          console.error('恢复默认配置失败:', error)
          this.$message.error('恢复默认失败')
        } finally {
          this.personalConfigSaving = false
        }
      }).catch(() => {
        // 用户取消
      })
    }
  }
}
</script>

<style lang="less" scoped>
.enterprise-query-config-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .general-config-section {
    margin-bottom: 20px;

    .config-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 20px;

      .config-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .config-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 600;
          color: #303133;

          i {
            margin-right: 8px;
            color: #409eff;
          }
        }
      }

      .config-content {
        display: flex;
        gap: 40px;

        .config-item {
          display: flex;
          align-items: center;

          .config-label {
            color: #606266;
            margin-right: 8px;
          }

          .config-value {
            color: #409eff;
            font-weight: 600;
            font-size: 16px;
          }
        }
      }
    }
  }

  .form-tip {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>

<template>
  <div class="role-list">

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>

    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
                @normalResetQuery="normalResetQuery"></SearchForm>

    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover>
      <el-table-column align="center" prop="roleName" label="角色名称"></el-table-column>
      <el-table-column align="center" label="角色性质">
        <template slot-scope="scope">
          <div>{{ roleTypeList | getRoleType(scope.row.roleType) }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="roleDesc" label="角色说明"></el-table-column>
      <el-table-column align="center" label="创建人" width="100">
        <template slot-scope="scope">{{ scope.row.createId | getNickName(scope.row.createId) }}</template>
      </el-table-column>
      <el-table-column align="center" prop="createTime" width="200" label="创建时间"></el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="update(scope.row)">编辑</el-button>
          <el-button class="btn-center" type="text" @click="del(scope.row)">删除</el-button>
          <el-button class="btn-center" type="text" @click="config(scope.row)">权限配置</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
                :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>


    <!-- 删除 -->
    <DtPopup :isShow.sync="showDelPopup" @close="showDelPopup = false" title="提示" center :footer="false" width="30%">
      <div class="check-popup">
        <div style="text-align: center">请确认是否删除？</div>
        <div class="btn-wrap">
          <el-button type="primary" class="btn-width"
                     :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px',width:'120px' }"
                     @click.stop="showDelPopup = false">取消</el-button>
          <el-button type="primary" class="btn-width"
                     :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color,width:'120px' }"
                     @click.stop="delHandle">确认</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 新增/编辑 -->
    <DtPopup :isShow.sync="showPopup" @close="closePopup" size="mini" :title="this.isAdd?'新增角色':'修改角色'" :footer="false">
      <el-form ref="addForm" :model="addData" label-width="120px" :rules="addRules" label-position="left">
        <div style="max-height:70vh;overflow:auto">
          <el-form-item label="角色名称" prop="roleName">
            <el-input v-model="addData.roleName" auto-complete="off" class="dt-input-width" placeholder="请输入角色名称" maxlength="20字" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="角色说明" prop="roleDesc">
            <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" placeholder="请输入角色说明" v-model="addData.roleDesc" maxlength="100字"
                      show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="角色性质" prop="roleType">
            <el-radio v-model="addData.roleType" v-for="(item, index) in roleTypeList" :key="index" :label="item.dicItemCode" :disabled="!isAdd">{{ item.dicItemName }}</el-radio>
          </el-form-item>
        </div>
          <div style="padding:20px 0;text-align: center">
            <el-button type="primary" class="btn-width"
                       :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px',width:'120px' }"
                       @click="closePopup">取消</el-button>
            <el-button type="primary" class="btn-width"
                       :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color,width:'120px' }"
                       @click="confirm">确认</el-button>
          </div>
      </el-form>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/roleManagement/index.js";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";

export default {
  name: "roleList",
  data() {
    return {
      toolListProps: {
        toolTitle: "角色管理",
        toolList: [
          {
            name: "新增角色",
            icon: "iconfont icondt8",
            // btnCode: "elms:role:add"
          }
        ]
      },
      tableData: [{}],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          roleName: ""
        }
      },
      searchFormTemp: [
        {
          label: "角色名称",
          name: "roleName",
          type: "input",
          width: "200px"
        },
        {
          label: "角色性质",
          name: "roleType",
          type: "select",
          width: "200px",
          list:[]
        }
      ],
      total: 0,
      showDelPopup: false,
      showPopup: false,
      isAdd:true,
      roleId: "",
      addData: {
        roleName: "",
        roleDesc: "",
        roleType: 99 // 默认其他
      },
      addRules: {
        roleName: [{ required: true, validator: validate, trigger: "blur"}]
      },
      roleTypeList:[]
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  filters: {
    getRoleType(list, roleType) {
      let index = list.findIndex((v) => v.dicItemCode == roleType);
      return index > -1 ? list[index].dicItemName : "-";
    }
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {
    async getDicFun() {
      let roleTypeListTemp = await getDicItemList("elms.role.type");
      this.roleTypeList = [];
      roleTypeListTemp.forEach(item => {
        this.roleTypeList.push({
          dicItemName: item.dicItemName,
          dicItemCode: parseInt(item.dicItemCode)
        })
      });

      this.searchFormTemp[1].list = this.roleTypeList;
    },
    async initData() {
      this.initParam.param.tenantId = this.$store.state.layoutStore.currentLoginUser.tenantId;
      let res = await api.getRolePage(this.initParam);
      if (res) {
        this.total = res.total;
        this.tableData = [];
        if (res.list) {
          this.tableData = res.list ? res.list : [{}];
        }
      }
    },
    handleTool(item) {
      if (item.name == "新增角色") {
        this.isAdd = true;
        this.showPopup = true;
      }
    },
    closePopup() {
      this.addData = _.cloneDeep(this.$options.data().addData);
      this.showPopup = false;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      });
    },

    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },
    update(row) {
      this.isAdd = false;
      this.addData.roleId = row.id;
      this.addData.roleName = row.roleName;
      this.addData.roleDesc = row.roleDesc;
      this.addData.roleType = row.roleType;
      this.showPopup = true;
    },
    config(row){
      this.$router.push({
        name:"permission",
        query:{
          roleId:row.id,
          roleName:row.roleName
        }
      })
    },
    async confirm() {
      if (!validateAlls(this.$refs.addForm)){return;}

      let res = await api.saveOrUpdate(this.addData);
      if (res) {
        this.$message({
          type: "success",
          message: this.isAdd ? "新增成功" : "修改成功"
        });
        this.initData();
        this.showPopup = false;
      }
    },
    del(row) {
      this.roleId = row.id;
      this.showDelPopup = true;
    },
    async delHandle() {
      let res = await api.deleteRole({ roleId: this.roleId });
      if (res) {
        this.$message({
          type: "success",
          message: "删除成功"
        });
        this.initData();
      }
      this.showDelPopup = false
    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    }
  }
};
</script>
<style lang="less">
.role-list {
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }
}



.check-popup {
  width: 100%;

  .btn-wrap {
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: center;

    .btn-width {
      width: 100px;
    }
  }

  .end-exam-text {
    margin-top: 10px;
  }
}
</style>

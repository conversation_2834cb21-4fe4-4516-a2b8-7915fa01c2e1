package com.kbao.kbcelms.riskMatrix.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 风险矩阵类别视图对象
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixCategoryVO {
    
    /**
     * 类别ID
     */
    private Long id;
    
    /**
     * 风险矩阵ID
     */
    private Long matrixId;
    
    /**
     * 类别名称
     */
    private String name;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 权重
     */
    private BigDecimal weight;
    
    /**
     * 权重（Double类型，用于前端显示）
     */
    private Double weightValue;
    
    /**
     * 计算方法
     */
    private String calculationMethod;
    
    /**
     * 计算方法名称
     */
    private String calculationMethodName;
    
    /**
     * 关联评分项ID列表
     */
    private List<Long> scoreItems;
    
    /**
     * 关联评分项名称列表
     */
    private List<String> scoreItemNames;
    
    /**
     * 档次配置列表
     */
    private List<RiskMatrixLevelVO> levels;
    
    /**
     * 排序序号
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 档次数量
     */
    private Integer levelCount;
}

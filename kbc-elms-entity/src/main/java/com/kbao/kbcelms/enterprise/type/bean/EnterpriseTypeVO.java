package com.kbao.kbcelms.enterprise.type.bean;

import com.kbao.kbcelms.enterprise.type.model.EnterpriseType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 企业类型展示VO
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ApiModel(value = "EnterpriseTypeVO", description = "企业类型展示VO")
public class EnterpriseTypeVO extends EnterpriseType {
    
    @ApiModelProperty(value = "员工规模范围展示文本")
    private String employeeRangeText;
    
    @ApiModelProperty(value = "营收规模范围展示文本")
    private String revenueRangeText;
}

package com.kbao.kbcelms.enterprise.type.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 企业类型实体
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@Document(collection = "enterprise_type")
@ApiModel(value = "EnterpriseType", description = "企业类型")
public class EnterpriseType implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    @NotBlank(message = "企业类型名称不能为空")
    @ApiModelProperty(value = "企业类型名称")
    private String name;

    @NotBlank(message = "企业类型编码不能为空")
    @Indexed(unique = true)
    @ApiModelProperty(value = "企业类型编码，唯一")
    private String code;

    @ApiModelProperty(value = "描述信息")
    private String description;

    @NotNull(message = "优先级不能为空")
    @ApiModelProperty(value = "优先级，数字越小优先级越高")
    private Integer priority;

    @Valid
    @ApiModelProperty(value = "判定规则列表")
    private List<EnterpriseTypeRule> rules;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    @ApiModelProperty(value = "创建人ID")
    private String createId;
    
    @ApiModelProperty(value = "更新人ID")
    private String updateId;
    
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @Transient
    private String employeeRangeText;

    @Transient
    private String revenueRangeText;
}

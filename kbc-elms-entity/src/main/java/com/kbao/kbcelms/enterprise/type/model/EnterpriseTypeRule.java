package com.kbao.kbcelms.enterprise.type.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 企业类型判定规则
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ApiModel(value = "EnterpriseTypeRule", description = "企业类型判定规则")
public class EnterpriseTypeRule implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @NotBlank(message = "规则字段不能为空")
    @ApiModelProperty(value = "规则字段：employeeCount-员工规模，revenue-营收规模")
    private String field;

    @ApiModelProperty(value = "最小值（可为空表示无下限）")
    private Long minValue;

    @ApiModelProperty(value = "最大值（可为空表示无上限）")
    private Long maxValue;
    
    public EnterpriseTypeRule() {}
    
    public EnterpriseTypeRule(String field, Long minValue, Long maxValue) {
        this.field = field;
        this.minValue = minValue;
        this.maxValue = maxValue;
    }
}

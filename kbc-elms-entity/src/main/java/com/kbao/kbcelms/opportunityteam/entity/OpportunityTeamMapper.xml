<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.opportunityteam.entity.OpportunityTeam">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="opportunityId" column="opportunity_id" jdbcType="INTEGER" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="userId" column="user_id" jdbcType="VARCHAR" />
        <result property="isDefault" column="is_default" jdbcType="INTEGER" />
        <result property="roleType" column="role_type" jdbcType="INTEGER" />
        <result property="joinType" column="join_type" jdbcType="INTEGER" />
        <result property="times" column="times" jdbcType="INTEGER" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
        <result property="isAgent" column="is_agent" jdbcType="INTEGER" />
        <result property="userName" column="user_name" jdbcType="VARCHAR" />
        <result property="nickname" column="nick_name" jdbcType="VARCHAR" />
        <result property="organNamePath" column="organ_name_path" jdbcType="VARCHAR" />


    </resultMap>

    <sql id="Base_Column_List">
        id, opportunity_id, tenant_id, user_id,is_default,role_type,  join_type, times, create_id, create_time, update_id, update_time, is_deleted,is_agent,user_name,nick_name,organ_name_path
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.opportunity_id,
        t.tenant_id,
        t.user_id,
        t.is_default,
        t.role_type,
        t.join_type,
        t.times,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted,
        t.is_agent,
        t.user_name,
        t.nick_name,
        t.organ_name_path
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="opportunityId != null">
                and t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
            </if>
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and t.user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="roleType != null">
                and t.role_type = #{roleType,jdbcType=INTEGER}
            </if>
            <if test="joinType != null">
                and t.join_type = #{joinType,jdbcType=INTEGER}
            </if>
            <if test="times != null">
                and t.times = #{times,jdbcType=INTEGER}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_opportunity_team where id = #{id} and is_deleted = 0
    </select>

    <select id="selectManager" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" />
            from t_opportunity_team
            where opportunity_id = #{opportunityId}
            and is_deleted = 0
            and role_type in (3,4,7)
    </select>

    <!-- 批量查询机会团队信息 -->
    <select id="selectManagers" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from t_opportunity_team
        where is_deleted = 0
        and role_type in (3,4,7)
        and opportunity_id in
        <foreach collection="opportunityIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_opportunity_team t
        <include refid="Base_Condition"/>
    </select>

    <select id="selectMember" resultType="com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember" >
        select
            IF(t.is_agent=1,t.nick_name,u.nick_name) as nickname,
            IF(t.is_agent=1,t.user_name ,u.bsc_use_name)   as userName,
            IF(t.is_agent=1,t.organ_name_path ,ut.organ_name_path)  as organNamePath,
            t.join_type,t.is_default,t.id,t.times,t.opportunity_id,t.role_type,t.user_id,
            u.email
        from t_opportunity_team t
         left join t_user u on t.user_id = u.user_id and u.is_deleted =0
         left join t_user_tenant ut on ut.user_id =u.user_id and ut.tenant_id = #{tenantId} and ut.is_deleted =0
        where
            t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
          and t.tenant_id = #{tenantId}
          and t.is_deleted =0

    </select>

    <select id="findOneMember" resultType="com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember" >
        select
            IF(t.is_agent=1,t.nick_name,u.nick_name) as nickname,
            IF(t.is_agent=1,t.user_name ,u.bsc_use_name)   as userName,
            IF(t.is_agent=1,t.organ_name_path ,ut.organ_name_path)  as organNamePath,
            t.join_type,t.is_default,t.id,t.times,t.opportunity_id,t.role_type,t.user_id,
            u.email
        from t_opportunity_team t
        left join t_user u on t.user_id = u.user_id and u.is_deleted =0
        left join t_user_tenant ut on ut.user_id =u.user_id and ut.tenant_id = #{tenantId} and ut.is_deleted =0
        where
        t.tenant_id = #{tenantId}
        and t.is_deleted =0
        <if test="id != null">
            and t.id = #{id}
        </if>
        <if test="opportunityId != null">
            and t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
        </if>
        <if test="roleType != null">
            and t.role_type=#{roleType}
        </if>
        limit 1
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_opportunity_team t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.opportunityteam.entity.OpportunityTeam" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_team (
            opportunity_id, tenant_id, user_id,is_default,role_type, join_type, times, create_id, create_time, update_id, update_time, is_deleted,is_agent,user_name,nick_name,organ_name_path
        ) values (
            #{opportunityId}, #{tenantId}, #{userId}, #{isDefault},#{roleType},#{joinType}, #{times}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0,#{isAgent},#{userName},#{nickname},#{organNamePath}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.opportunityteam.entity.OpportunityTeam" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">opportunity_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="roleType != null">role_type,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="joinType != null">join_type,</if>
            <if test="times != null">times,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isAgent != null">is_agent,</if>
            <if test="userName != null">user_name,</if>
            <if test="nickname != null">nick_name,</if>
            <if test="organNamePath != null">organ_name_path,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">#{opportunityId},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="roleType != null">#{roleType},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="joinType != null">#{joinType},</if>
            <if test="times != null">#{times},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isAgent != null">#{isAgent},</if>
            <if test="userName != null">#{userName},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="organNamePath != null">#{organNamePath},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.opportunityteam.entity.OpportunityTeam">
        update t_opportunity_team set
            opportunity_id = #{opportunityId},
            tenant_id = #{tenantId},
            user_id = #{userId},
            role_type = #{roleType},
            is_default = #{isDefault},
            join_type = #{joinType},
            times = #{times},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted},
            is_agent = #{isAgent},
            user_name = #{userName},
            nick_name = #{nickname},
            organ_name_path = #{organNamePath}
        where id = #{id}
    </update>

    <update id="updateMember" parameterType="com.kbao.kbcelms.opportunityteam.entity.OpportunityTeam">
        update t_opportunity_team set
        <if test="joinType != null">join_type = #{joinType},</if>
        <if test="roleType != null">role_type = #{roleType},</if>
          update_id = #{updateId},
          update_time = #{updateTime}
        where
            opportunity_id = #{opportunityId}
        and user_id = #{userId}
        and tenant_id = #{tenantId}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.opportunityteam.entity.OpportunityTeam">
        update t_opportunity_team
        <set>
            <if test="opportunityId != null">opportunity_id = #{opportunityId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="roleType != null">role_type = #{roleType},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="joinType != null">join_type = #{joinType},</if>
            <if test="times != null">times = #{times},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="isAgent != null">is_agent = #{isAgent},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="nickname != null">nick_name = #{nickname},</if>
            <if test="organNamePath != null">organ_name_path = #{organNamePath},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_opportunity_team set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_opportunity_team (
            opportunity_id, tenant_id, user_id,role_type, is_default, join_type, times, create_id, create_time, update_id, update_time, is_deleted,is_agent,user_name,nick_name,organ_name_path
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.opportunityId}, #{item.tenantId}, #{item.userId},#{item.roleType},#{item.isDefault},  #{item.joinType}, #{item.times}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0,#{item.isAgent},#{item.userName},#{item.nickname},#{item.organNamePath}
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_opportunity_team set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByOpportunityId" >
        update t_opportunity_team
        set is_deleted = 1,update_time=now()
        where opportunity_id= #{opportunityId}
    </delete>

    <!-- 查询待参与的机会ID列表 -->
    <select id="selectPendingParticipationOpportunityIds" resultType="java.lang.Integer" parameterType="java.util.Map">
        select distinct opportunity_id
        from t_opportunity_team
        where user_id = #{userId}
        and tenant_id = #{tenantId}
        and join_type = #{joinType}
        and role_type in
        <foreach collection="roleTypes" item="roleType" open="(" separator="," close=")">
            #{roleType}
        </foreach>
        and is_deleted = 0
        order by opportunity_id desc
    </select>

    <!-- 分页查询待参与的机会ID列表 -->
    <select id="selectPendingParticipationOpportunityIdsPage" resultType="java.lang.Integer" parameterType="java.util.Map">
        select distinct opportunity_id
        from t_opportunity_team
        where user_id = #{userId}
        and tenant_id = #{tenantId}
        and join_type = #{joinType}
        and role_type in
        <foreach collection="roleTypes" item="roleType" open="(" separator="," close=")">
            #{roleType}
        </foreach>
        and is_deleted = 0
        order by opportunity_id desc
    </select>

</mapper> 
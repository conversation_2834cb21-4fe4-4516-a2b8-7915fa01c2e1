package com.kbao.kbcelms.genAgentEnterprise.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListReqVo;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListResVo;import com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.type.model.EnterpriseType;
import com.kbao.kbcelms.enterprise.type.model.EnterpriseTypeRule;
import com.kbao.kbcelms.enterprise.type.service.EnterpriseTypeService;
import com.kbao.kbcelms.enterprise.util.EnterpriseTypeRuleUtil;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 顾问企业表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Service
public class GenAgentEnterpriseService extends BaseSQLServiceImpl<GenAgentEnterprise, Integer, GenAgentEnterpriseMapper> {

    /**
     * 分页查询顾问企业列表
     * @param pageRequest 分页查询参数
     * @return 分页结果
     */
    public PageInfo<AgentEnterpriseListResVo> getAgentEnterpriseList(PageRequest<AgentEnterpriseListReqVo> pageRequest) {
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<AgentEnterpriseListResVo> list = mapper.getAgentEnterpriseList(pageRequest.getParam());
        return new PageInfo<>(list);
    }

    /*
                 根据basicInfo保存GenAgentEnterprise，
                 注意dtType需要根据EnterpriseTypeRule的employeeCount-员工规模，revenue-营收规模进行判断，必须完全满足条件，
                 然后取EnterpriseType的code属性，保存到dtType
                 GenAgentEnterprise的人员规模staffScale 需要取basicInfo的staffNumRange属性，注意staffNumRange格式为1000-4999人或者小于50人，你需要取出最后一个数字，
                 然后取出所有的EnterpriseTypeRule的employeeCount部分，转换成employeeRangeText取值
                 营收规模enterpriseScale，则需要根据basicInfo的latestBusinessIncome属性，匹配出EnterpriseTypeRule中满足条件的revenue，转换成revenueRangeText取值
                 取值逻辑参考enrichEnterpriseTypeDisplayText方法。
             */
}

package com.kbao.kbcelms.enterprise.base.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.enterprise.base.dao.EnterpriseBasicInfoDao;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 企业基本信息业务逻辑层
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class EnterpriseBasicInfoService extends BaseMongoServiceImpl<EnterpriseBasicInfo, String, EnterpriseBasicInfoDao> {

    /**
     * 分页查询企业基本信息列表
     * @param page 分页查询参数
     * @return 分页结果
     */
    public PageInfo<EnterpriseBasicInfo> page(PageRequest<EnterpriseBasicInfo> page) {
        EnterpriseBasicInfo queryParam = page.getParam();
        String tenantId = SysLoginUtils.getUser().getTenantId();

        Criteria criteria = new Criteria();
        // 添加租户条件
        criteria.and("tenantId").is(tenantId);

        // 添加查询条件
        if (queryParam != null) {
            if (StringUtils.hasText(queryParam.getName())) {
                criteria.and("name").regex(queryParam.getName(), "i"); // 忽略大小写的模糊查询
            }
            if (StringUtils.hasText(queryParam.getCreditCode())) {
                criteria.and("creditCode").is(queryParam.getCreditCode());
            }
            if (StringUtils.hasText(queryParam.getRegStatus())) {
                criteria.and("regStatus").is(queryParam.getRegStatus());
            }
            if (StringUtils.hasText(queryParam.getIndustry())) {
                criteria.and("industry").regex(queryParam.getIndustry(), "i"); // 忽略大小写的模糊查询
            }
        }

        Pagination<EnterpriseBasicInfo> pagination = new Pagination<>(page.getPageNum(), page.getPageSize(), "updateTime");
        return super.page(new Query(criteria), pagination);
    }

    /**
     * 根据统一社会信用代码查询企业基本信息
     * @param creditCode 统一社会信用代码
     * @return 企业基本信息
     */
    public EnterpriseBasicInfo findByCreditCode(String creditCode) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        return dao.findByCreditCode(creditCode, tenantId);
    }
    
    /**
     * 根据企业名称查询企业基本信息
     * @return 企业基本信息
     */
    public List<EnterpriseBasicInfo> searchByName(String name) {
        return dao.searchByName(name);
    }

    public EnterpriseBasicInfo queryByFullName(String name) {
        return dao.queryByFullName(name);
    }
    
    /**
     * 保存或更新企业基本信息
     * @param enterpriseBasicInfo 企业基本信息
     * @return 保存后的企业基本信息
     */
    public EnterpriseBasicInfo saveOrUpdate(EnterpriseBasicInfo enterpriseBasicInfo) {
        Date now = new Date();

        // 设置租户ID
        if (enterpriseBasicInfo.getTenantId() == null) {
            enterpriseBasicInfo.setTenantId(SysLoginUtils.getUser().getTenantId());
        }

        // 从staffNumRange中提取最后一个数字并设置到staffNum
        extractStaffNumFromRange(enterpriseBasicInfo);

        // 检查是否已存在
        EnterpriseBasicInfo existing = findByCreditCode(enterpriseBasicInfo.getCreditCode());
        if (existing != null) {
            // 更新现有记录
            enterpriseBasicInfo.setId(existing.getId());
            enterpriseBasicInfo.setCreateTime(existing.getCreateTime());
            enterpriseBasicInfo.setUpdateTime(now); // 设置更新时间
        } else {
            // 新建记录
            enterpriseBasicInfo.setCreateTime(now);
            enterpriseBasicInfo.setUpdateTime(now);
        }

        return dao.saveOrUpdate(enterpriseBasicInfo);
    }

    /**
     * 从staffNumRange中提取最后一个数字并设置到staffNum字段
     * 例如：≥1000人<5000人 -> 5000
     * @param enterpriseBasicInfo 企业基本信息
     */
    private void extractStaffNumFromRange(EnterpriseBasicInfo enterpriseBasicInfo) {
        String staffNumRange = enterpriseBasicInfo.getStaffNumRange();
        if (!StringUtils.hasText(staffNumRange)) {
            return;
        }

        // 正则表达式匹配所有数字
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(staffNumRange);

        Integer lastNumber = null;
        // 找到最后一个数字
        while (matcher.find()) {
            try {
                lastNumber = Integer.parseInt(matcher.group());
            } catch (NumberFormatException e) {
                // 忽略解析失败的情况
            }
        }

        if (lastNumber != null) {
            enterpriseBasicInfo.setStaffNum(lastNumber);
        }
    }
}

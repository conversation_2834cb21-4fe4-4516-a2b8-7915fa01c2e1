package com.kbao.kbcelms.bsc;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.*;
import com.kbao.kbcbsc.appfilechannel.bean.AppFileChannelListVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantConfigResVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantIdReq;
import com.kbao.kbcbsc.apptenant.bean.AppTenantListVo;
import com.kbao.kbcbsc.company.model.CompanyListForWebVo;
import com.kbao.kbcbsc.dicitems.entity.DicItems;
import com.kbao.kbcbsc.organization.entity.Organization;
import com.kbao.kbcbsc.organization.model.OrgIdReq;
import com.kbao.kbcbsc.organization.model.OrgTypeReq;
import com.kbao.kbcbsc.tenant.bean.AppUserVo;
import com.kbao.kbcbsc.tenant.bean.TenantConfigInfoVo;
import com.kbao.kbcbsc.tenant.bean.TenantIdReq;
import com.kbao.kbcbsc.tenant.bean.TenantVo;
import com.kbao.kbcbsc.tenant.entity.Tenant;
import com.kbao.kbcbsc.user.model.UserIdReq;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.outinf.vo.OrgCodesRequestVO;
import com.kbao.kbcelms.outinf.vo.TenantIdRequestVO;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/24 11:10
 */
@Service
@Slf4j
public class BscClientService {

    @Autowired
    private DicItemsClientAdapter dicItemsClientAdapter;

    @Autowired
    private UserClientAdapter userClientAdapter;

    @Autowired
    private TenantClientAdapter tenantClientAdapter;

    @Autowired
    AppTenantClientAdapter appTenantClientAdapter;

    @Autowired
    OrgClientAdapter orgClientAdapter;
    @Autowired
    private CompanyClientAdapter companyClientAdapter;

    /**
     * 获取快保云服字典列表
     *
     * @param dicCode:
     * <AUTHOR>
     * @date 2021/12/31 11:37
     */
    public Result<List<DicItems>> getDicItems(String dicCode) {
        Result<List<DicItems>> rs = dicItemsClientAdapter.getDicItems(dicCode);
        if(ResultStatusEnum.SUCCESS.getStatus().equals(rs.getResp_code())){
            return rs;
        }else{
            throw new BusinessException(rs.getResp_msg());
        }
    }

    /**
     * 获取当前登录用户在用户应用下的租户列表
     */
    public Result<List<AppTenantListVo>> getWebUserTenants() {
        AppUserVo req = new AppUserVo();
        req.setAppId(BscUserUtils.getUser().getFunction().getApplyId());
        req.setUserId(BscUserUtils.getUserId());
        return tenantClientAdapter.getAppUserTenantList(req);
    }

    public Result<List<UserIdReq>> getTenantUsers() {
        TenantIdReq tenantIdReq = new TenantIdReq();
        tenantIdReq.setTenantId(SysLoginUtils.getUser().getTenantId());
        return userClientAdapter.getTenantUsers(tenantIdReq);
    }

    /**
     * @param tenantId
     * @param appCode
     * @param fileType
     * @return
     * @Author: wangyl
     * @Description: 获取文件渠道配置
     * @Date: 2020/7/20 
     */
    public AppFileChannelListVo getFileChannelConfig(String tenantId, String appCode, String fileType) {
        AppTenantIdReq req = new AppTenantIdReq();
        req.setTenantId(tenantId);
        //if (!platformConfig.isWeb()) {
        req.setAppId(appCode);
        //}
        req.setAppCode(appCode);
        Result<AppTenantConfigResVo> rs = appTenantClientAdapter.getChannelConfigInfo(req);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(rs.getResp_code())) {
            AppTenantConfigResVo appTenantConfigResVo = rs.getDatas();
            if (null != appTenantConfigResVo) {
                List<AppFileChannelListVo> fileChannelList = appTenantConfigResVo.getFileChannelList();
                if (CollectionUtils.isNotEmpty(fileChannelList)) {
                    for (AppFileChannelListVo vo : fileChannelList) {
                        if (StringUtils.equals(fileType, vo.getFileType())) {
                            return vo;
                        }
                    }
                }
            }
            log.error("getFileChannelConfig >> request >> 文件渠道未配置，请求：{}，结果：{}", JSON.toJSONString(req), JSON.toJSONString(rs));
            throw new BusinessException("文件渠道未配置");
        } else {
            throw new BusinessException(rs.getResp_msg());
        }
    }

    /**
     * @param tenantId
     * @return
     * @Author: wangyl
     * @Description: 获取租户信息
     * @Date: 2020/7/22 
     */
    public Tenant getTenant(String tenantId) {
        Result<TenantConfigInfoVo> rs = tenantClientAdapter.getTenantConfigInfo(new TenantVo(tenantId));
        if (ResultStatusEnum.SUCCESS.getStatus().equals(rs.getResp_code())) {
            TenantConfigInfoVo tenantConfigInfoVo = rs.getDatas();
            if (null != tenantConfigInfoVo.getTenant()) {
                return tenantConfigInfoVo.getTenant();
            }
            throw new BusinessException("消息渠道未配置");
        } else {
            throw new BusinessException(rs.getResp_msg());
        }
    }

    /**
     * 查询所有法人分公司
     * @param requestVO 请求参数，包含租户ID（可选）
     * @return
     */
    public Result<List<Organization>> getLegalList( TenantIdRequestVO requestVO) {
        OrgTypeReq orgTypeReq = new OrgTypeReq();
        orgTypeReq.setOrgType("2");

        // 如果请求参数为空或租户ID为空，则使用当前用户的租户ID
        String tenantId = (requestVO != null && requestVO.getTenantId() != null) ?
                requestVO.getTenantId() : SysLoginUtils.getUser().getTenantId();

        orgTypeReq.setTenantId(tenantId);
        Result<List<Organization>> organsByType = orgClientAdapter.getOrgansByType(orgTypeReq);
        return organsByType;
    }

    /**
     * 查询营业部
     * @param requestVO
     * @return
     */
    public Result<List<Organization>> getTransactions( OrgCodesRequestVO requestVO) {
        if (requestVO.getOrgCodes() == null || requestVO.getOrgCodes().isEmpty()) {
            return Result.succeed(Collections.emptyList(), "组织机构编码列表不能为空");
        }

        // 如果租户ID为空，则使用当前用户的租户ID
        String tenantId = (requestVO.getTenantId() != null) ?
                requestVO.getTenantId() : SysLoginUtils.getUser().getTenantId();

        // 获取所有组织机构数据
        OrgIdReq orgIdReq = new OrgIdReq();
        orgIdReq.setTenantId(tenantId);
        Result<List<Organization>> organsByType = orgClientAdapter.getOrgList(orgIdReq);

        if (organsByType.getDatas() == null || organsByType.getDatas().isEmpty()) {
            return Result.succeed(Collections.emptyList(), "未找到组织机构数据");
        }

        // 递归查找指定编码下层节点中orgType为4的数据
        List<Organization> result = new ArrayList<>();
        for (String orgCode : requestVO.getOrgCodes()) {
            List<Organization> foundOrgs = findOrgType4InChildren(organsByType.getDatas(), orgCode);
            result.addAll(foundOrgs);
        }

        // 去重
        List<Organization> uniqueResult = result.stream()
                .collect(Collectors.toMap(
                        Organization::getOrgCode,
                        org -> org,
                        (existing, replacement) -> existing
                ))
                .values()
                .stream()
                .collect(Collectors.toList());

        organsByType.setDatas(uniqueResult);
        return organsByType;
    }

    /**
     * 递归查找指定组织机构编码下层节点中orgType为4的数据
     * @param organizations 组织机构列表
     * @param targetOrgCode 目标组织机构编码
     * @return orgType为4的组织机构列表
     */
    private List<Organization> findOrgType4InChildren(List<Organization> organizations, String targetOrgCode) {
        List<Organization> result = new ArrayList<>();

        for (Organization org : organizations) {
            if (targetOrgCode.equals(org.getOrgCode())) {
                // 找到目标组织机构，递归查找其子节点中orgType为4的数据
                findOrgType4Recursively(org, result);
                break;
            } else if (org.getChildren() != null && !org.getChildren().isEmpty()) {
                // 递归查找子节点
                List<Organization> childResult = findOrgType4InChildren(org.getChildren(), targetOrgCode);
                result.addAll(childResult);
            }
        }

        return result;
    }

    /**
     * 递归查找组织机构及其子节点中orgType为4的数据
     * @param organization 组织机构
     * @param result 结果列表
     */
    private void findOrgType4Recursively(Organization organization, List<Organization> result) {
        // 检查当前节点是否为orgType为4
        if ("4".equals(organization.getOrgType())) {
            result.add(organization);
        }

        // 递归查找子节点
        if (organization.getChildren() != null && !organization.getChildren().isEmpty()) {
            for (Organization child : organization.getChildren()) {
                findOrgType4Recursively(child, result);
            }
        }
    }

    /**
     * 获取所有保险公司列表
     *
     * @return
     */
    public Result<List<CompanyListForWebVo>> getCompanyList(){
        return this.companyClientAdapter.getAllCompanyList();
    }
}

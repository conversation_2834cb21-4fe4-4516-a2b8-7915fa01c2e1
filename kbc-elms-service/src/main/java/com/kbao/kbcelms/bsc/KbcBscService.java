package com.kbao.kbcelms.bsc;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.AppTenantClientAdapter;
import com.kbao.kbcbsc.adapter.DicItemsClientAdapter;
import com.kbao.kbcbsc.adapter.ParamClientAdapter;
import com.kbao.kbcbsc.adapter.TenantClientAdapter;
import com.kbao.kbcbsc.apptenant.bean.AppTenantConfigResVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantIdReq;
import com.kbao.kbcbsc.apptenant.bean.AppTenantListVo;
import com.kbao.kbcbsc.client.WechatServiceClientService;
import com.kbao.kbcbsc.dicitems.entity.DicItems;
import com.kbao.kbcbsc.param.entity.Param;
import com.kbao.kbcbsc.tenant.bean.AppUserVo;
import com.kbao.kbcbsc.tenant.bean.TenantConfigInfoVo;
import com.kbao.kbcbsc.tenant.bean.TenantIdReq;
import com.kbao.kbcbsc.tenant.bean.TenantVo;
import com.kbao.kbcbsc.user.model.UserIdReq;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcbsc.wechat.request.AppParam;
import com.kbao.kbcbsc.wechat.request.WechatParam;
import com.kbao.kbcbsc.wechat.response.SignatureVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: bsc服务
 * @author: husw
 * @create: 2023-05-19 15:44
 **/
@Service
@Slf4j
public class KbcBscService {


    @Autowired
    private TenantClientAdapter tenantClientAdapter;

    @Autowired
    AppTenantClientAdapter appTenantClientAdapter;

    @Autowired
    private WechatServiceClientService wechatServiceClientService;

    /**
     * 获取当前登录用户在用户应用下的租户列表
     */
    public Result<List<AppTenantListVo>> getWebUserTenants() {
        AppUserVo req = new AppUserVo();
        req.setAppId(BscUserUtils.getUser().getFunction().getApplyId());
        req.setUserId(BscUserUtils.getUserId());
        return tenantClientAdapter.getAppUserTenantList(req);
    }

    /**
     * 获取当前租户下的所有Web用户信息
     *
     * @return
     */
    public Result<List<UserIdReq>> getTenantUsers() {
        TenantIdReq tenantIdReq = new TenantIdReq();
        tenantIdReq.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
        return tenantClientAdapter.getTenantUsers(tenantIdReq);
    }

    /**
     * @Description 查询租户的基本信息
     * @since V1.0
     * <AUTHOR>
     * @param: [tenantId]
     * @return: com.kbao.kbcbsc.tenant.bean.TenantConfigInfoVo
     * 2021/1/6  9:13
     *
     */
    public TenantConfigInfoVo getTenantConfigInfo(String tenantId){
        TenantVo vo = new TenantVo();
        vo.setTenantId(tenantId);
        Result<TenantConfigInfoVo> tenantConfigInfo = tenantClientAdapter.getTenantConfigInfo(vo);
        log.debug("查询租户的基本信息为:{}",tenantConfigInfo);
        Assert.isTrue(ResultStatusEnum.isSuccess(tenantConfigInfo.getResp_code()), tenantConfigInfo.getResp_msg());
        return tenantConfigInfo.getDatas();
    }

    public AppParam getAppParam(WechatParam wechatParam, String tenantId, String appCode, String serviceType) {
        AppParam appParam = new AppParam();
        appParam.setTenantId(tenantId);
        appParam.setAppCode(appCode);
        appParam.setServiceType(serviceType);
        appParam.setWechatParam(wechatParam);
        return appParam;
    }

    public SignatureVO getSignature(AppParam appParam) {
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", appParam);
        Result<SignatureVO> signature = wechatServiceClientService.getSignature(appParam);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", signature);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(signature.getResp_code())) {
            return signature.getDatas();
        } else {
            throw new BusinessException("获取签字失败");
        }
    }




}

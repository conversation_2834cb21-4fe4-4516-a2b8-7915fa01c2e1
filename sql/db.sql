-- --------------------------------------------------------
-- 主机:                           mysql-kbcs-test-lan.kbao123.com
-- 服务器版本:                        5.7.24-log - MySQL Community Server (GPL)
-- 服务器操作系统:                      Linux
-- HeidiSQL 版本:                  12.3.0.6589
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 kbc_elms_sta.t_auth 结构
CREATE TABLE IF NOT EXISTS `t_auth` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '功能编号',
    `auth_code` varchar(64) NOT NULL COMMENT '权限编号',
    `auth_name` varchar(32) DEFAULT NULL COMMENT '权限位名称',
    `parent_code` varchar(255) DEFAULT NULL COMMENT '权限路由',
    `auth_desc` varchar(500) DEFAULT NULL COMMENT '权限说明',
    `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
    `sort` int(11) DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `auth_code` (`auth_code`),
    KEY `index_sort` (`sort`) USING BTREE,
    KEY `index_func_id` (`id`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='功能权限位';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_bas_code 结构
CREATE TABLE IF NOT EXISTS `t_bas_code` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
    `code` varchar(12) NOT NULL COMMENT '编码',
    `code_type` varchar(10) DEFAULT NULL COMMENT '状态',
    `name` varchar(20) NOT NULL COMMENT '名称',
    `parent_code` varchar(10) DEFAULT NULL COMMENT '父级编码',
    `pinyin_code` varchar(10) DEFAULT NULL COMMENT 'PINYIN',
    `enable` varchar(10) DEFAULT NULL COMMENT '是否可用',
    `sort` varchar(10) DEFAULT NULL COMMENT '排序',
    `remark` varchar(10) DEFAULT NULL COMMENT '注释',
    `post_code` varchar(10) DEFAULT NULL,
    `company_id` varchar(20) NOT NULL COMMENT '保司ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code_company_id_0` (`code`,`company_id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=15399 DEFAULT CHARSET=utf8;

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_constant_config 结构
CREATE TABLE IF NOT EXISTS `t_constant_config` (
                                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '常数ID',
    `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '常数名称',
    `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '常数编码',
    `description` text COLLATE utf8mb4_unicode_ci COMMENT '常数描述',
    `category` tinyint(4) NOT NULL DEFAULT '1' COMMENT '分类：1-系统参数，2-数学常数，3-业务常数，4-系数参数',
    `data_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string' COMMENT '数据类型：string-字符串，number-数值，boolean-布尔值，date-日期，json-JSON',
    `constant_value` text COLLATE utf8mb4_unicode_ci COMMENT '常数值',
    `default_value` text COLLATE utf8mb4_unicode_ci COMMENT '默认值',
    `unit` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位',
    `min_value` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最小值（数值类型）',
    `max_value` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最大值（数值类型）',
    `value_range` text COLLATE utf8mb4_unicode_ci COMMENT '取值范围说明',
    `is_system` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否系统常数：0-否，1-是',
    `is_editable` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否可编辑：0-否，1-是',
    `usage_count` int(11) NOT NULL DEFAULT '0' COMMENT '使用次数',
    `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建用户',
    `update_user` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新用户',
    `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_name` (`name`),
    KEY `idx_category` (`category`),
    KEY `idx_data_type` (`data_type`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`),
    KEY `idx_is_system` (`is_system`),
    KEY `idx_usage_count` (`usage_count`),
    KEY `idx_category_status` (`category`,`status`),
    KEY `idx_status_deleted` (`status`,`deleted`),
    KEY `idx_data_type_status` (`data_type`,`status`)
    ) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='常数配置表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_data_template 结构
CREATE TABLE IF NOT EXISTS `t_data_template` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `template_name` varchar(50) NOT NULL COMMENT '模板名称',
    `biz_code` varchar(50) NOT NULL COMMENT '模板编号',
    `type` varchar(50) DEFAULT NULL COMMENT '分类信息',
    `status` char(1) DEFAULT '0' COMMENT '状态：0-禁用，1-启用',
    `remark` varchar(500) DEFAULT NULL COMMENT '描述',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_id` varchar(50) NOT NULL COMMENT '创建人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_id` varchar(50) DEFAULT NULL COMMENT '更新人',
    `is_deleted` tinyint(4) NOT NULL COMMENT '是否删除 0-未删除 1-已删除',
    `tenant_id` varchar(10) NOT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `uniq_biz_code` (`biz_code`)
    ) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='数据模板表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_formula 结构
CREATE TABLE IF NOT EXISTS `t_formula` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公式名称',
    `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公式描述',
    `formula` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公式内容',
    `category` tinyint(4) NOT NULL COMMENT '分类：1-战略文化与治理框架，2-组织责任与指标体系，3-风险评估与流程执行，4-沟通系统与技术支持',
    `enterprise_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业类型：A-大型企业，B-中型企业，C-小型企业',
    `status` tinyint(4) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `version` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '1.0' COMMENT '版本号',
    `usage_count` int(11) DEFAULT '0' COMMENT '使用次数',
    `last_test_time` datetime DEFAULT NULL COMMENT '最后测试时间',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
    `update_user` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
    `deleted` tinyint(4) DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category`),
    KEY `idx_enterprise_type_id` (`enterprise_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
    ) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公式表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_formula_calculation_log 结构
CREATE TABLE IF NOT EXISTS `t_formula_calculation_log` (
                                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `formula_id` bigint(20) NOT NULL COMMENT '公式ID',
    `formula_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公式名称',
    `input_variables` json DEFAULT NULL COMMENT '输入变量',
    `calculation_result` decimal(10,4) DEFAULT NULL COMMENT '计算结果',
    `calculation_time` datetime DEFAULT NULL COMMENT '计算时间',
    `execution_time_ms` int(11) DEFAULT NULL COMMENT '执行时间（毫秒）',
    `status` tinyint(4) DEFAULT NULL COMMENT '计算状态：1-成功，0-失败',
    `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
    `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户ID',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_formula_id` (`formula_id`),
    KEY `idx_calculation_time` (`calculation_time`),
    KEY `idx_user_id` (`user_id`),
    CONSTRAINT `t_formula_calculation_log_ibfk_1` FOREIGN KEY (`formula_id`) REFERENCES `t_formula` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公式计算记录表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_formula_variable 结构
CREATE TABLE IF NOT EXISTS `t_formula_variable` (
                                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `formula_id` bigint(20) NOT NULL COMMENT '公式ID',
    `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变量名称',
    `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变量类型：number-数值，variable-变量，constant-常数',
    `default_value` decimal(10,4) DEFAULT NULL COMMENT '默认值',
    `min_value` decimal(10,4) DEFAULT NULL COMMENT '最小值',
    `max_value` decimal(10,4) DEFAULT NULL COMMENT '最大值',
    `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '变量描述',
    `unit` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(4) DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_formula_id` (`formula_id`),
    CONSTRAINT `t_formula_variable_ibfk_1` FOREIGN KEY (`formula_id`) REFERENCES `t_formula` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公式变量表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_gen_agent_enterprise 结构
CREATE TABLE IF NOT EXISTS `t_gen_agent_enterprise` (
                                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(50) DEFAULT NULL COMMENT '企业名称',
    `creditCode` varchar(50) DEFAULT NULL COMMENT '社会统一信用代码',
    `categoryCode` varchar(10) DEFAULT NULL COMMENT '行业代码',
    `categoryName` varchar(100) DEFAULT NULL COMMENT '行业类名',
    `dtType` varchar(20) DEFAULT NULL COMMENT '企业类型',
    `enterpriseScale` varchar(20) DEFAULT NULL COMMENT '企业规模',
    `city` varchar(30) DEFAULT NULL COMMENT '企业所在城市',
    `districtCode` varchar(20) DEFAULT NULL COMMENT '行政区划代码',
    `staffScale` varchar(30) DEFAULT NULL COMMENT '人员规模',
    `annualIncome` varchar(20) DEFAULT NULL COMMENT '企业年收入',
    `isVerified` char(1) DEFAULT NULL COMMENT '是否验真',
    `enterpriseContacter` varchar(20) DEFAULT NULL COMMENT '企业联系人',
    `contacterPhone` varchar(20) DEFAULT NULL COMMENT '联系人电话',
    `remark` varchar(300) DEFAULT NULL COMMENT '备注信息',
    `createId` varchar(50) DEFAULT NULL COMMENT '创建人 当前用户ID',
    `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updateId` varchar(50) DEFAULT NULL COMMENT '更新人 默认为当前时间',
    `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `isDeleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
    `tenantId` varchar(10) NOT NULL DEFAULT 'T0001' COMMENT '租户ID',
    `agentCode` varchar(30) DEFAULT NULL COMMENT '顾问工号',
    PRIMARY KEY (`id`),
    KEY `idx_field_creditCode` (`creditCode`),
    KEY `idx_field_agentCode` (`agentCode`),
    KEY `t_gen_agent_enterprise_dtType_IDX` (`dtType`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COMMENT='顾问企业信息表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_industry 结构
CREATE TABLE IF NOT EXISTS `t_industry` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `code` varchar(20) NOT NULL COMMENT '行业代码',
    `name` varchar(100) NOT NULL COMMENT '行业名称',
    `level` tinyint(4) NOT NULL COMMENT '行业级别(1-门类,2-大类,3-中类,4-小类)',
    `parent_code` varchar(20) DEFAULT NULL COMMENT '父级行业代码',
    `full_path` varchar(200) DEFAULT NULL COMMENT '完整路径(如A/01/011/0111)',
    `full_name` varchar(300) DEFAULT NULL COMMENT '完整名称(如农/农业/谷物种植/稻谷种植)',
    `is_valid` tinyint(4) DEFAULT '1' COMMENT '是否有效(0-无效,1-有效)',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序字段',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_class_code` (`code`),
    KEY `idx_parent_code` (`parent_code`),
    KEY `idx_level` (`level`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1770 DEFAULT CHARSET=utf8mb4 COMMENT='行业分类表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity 结构
CREATE TABLE IF NOT EXISTS `t_opportunity` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `biz_code` varchar(50) NOT NULL COMMENT '机会编码',
    `agent_code` varchar(50) NOT NULL COMMENT '顾问工号',
    `agent_name` varchar(50) DEFAULT NULL COMMENT '顾问姓名',
    `opportunity_name` varchar(50) DEFAULT NULL COMMENT '机会名称',
    `agent_enterprise_id` int(11) DEFAULT NULL COMMENT '关联顾问企业ID',
    `opportunity_type` char(2) DEFAULT NULL COMMENT '机会类型: 1-员服，2-综合',
    `industry_code` varchar(10) DEFAULT NULL COMMENT '关联行业编码',
    `status` int(11) DEFAULT NULL COMMENT '机会状态：0-待提交，1-已提交，2-锁定，3-中止，4-终止',
    `area_center_code` varchar(50) DEFAULT NULL COMMENT '区域中心编码',
    `area_center_name` varchar(50) DEFAULT NULL COMMENT '区域中心名称',
    `legal_code` varchar(50) DEFAULT NULL COMMENT '法人公司编码',
    `legal_name` varchar(50) DEFAULT NULL COMMENT '法人公司名称',
    `company_code` varchar(50) DEFAULT NULL COMMENT '分公司编码',
    `company_name` varchar(50) DEFAULT NULL COMMENT '分公司名称',
    `trading_center_code` varchar(50) DEFAULT NULL COMMENT '交易服务中心编码',
    `trading_center_name` varchar(50) DEFAULT NULL COMMENT '交易服务中心名称',
    `sales_center_code` varchar(50) DEFAULT NULL COMMENT '销售服务中心编码',
    `sales_center_name` varchar(50) DEFAULT NULL COMMENT '销售服务中心名称',
    `process_step` varchar(20) DEFAULT NULL COMMENT '流程步骤',
    `current_process_id` int(11) DEFAULT NULL COMMENT '当前流程ID',
    `coordinator` varchar(50) DEFAULT NULL COMMENT '统筹人员id',
    `project_manager` varchar(50) DEFAULT NULL COMMENT '项目经理id',
    `project_org_code` varchar(50) DEFAULT NULL COMMENT '项目归属机构code',
    `project_org_name` varchar(50) DEFAULT NULL COMMENT '项目归属机构名称',
    `create_id` varchar(50) DEFAULT NULL COMMENT '创建人 当前用户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id` varchar(50) DEFAULT NULL COMMENT '更新人 默认为当前时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` int(11) DEFAULT NULL COMMENT '是否删除',
    `tenant_id` varchar(10) DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_biz_code` (`biz_code`),
    KEY `idx_company_id` (`agent_enterprise_id`),
    KEY `idx_industry_id` (`industry_code`),
    KEY `t_opportunity_tenant_id_IDX` (`tenant_id`) USING BTREE,
    KEY `t_opportunity_is_deleted_IDX` (`is_deleted`) USING BTREE,
    KEY `t_opportunity_company_code_IDX` (`company_code`) USING BTREE,
    KEY `t_opportunity_industry_code_IDX` (`industry_code`) USING BTREE,
    KEY `t_opportunity_opportunity_type_IDX` (`opportunity_type`) USING BTREE,
    KEY `t_opportunity_project_manager_IDX` (`project_manager`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='机会表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_detail 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_detail` (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `opportunity_id` int(11) NOT NULL COMMENT '机会ID',
    `insure_num` int(11) DEFAULT NULL COMMENT '投保人员规模',
    `has_history_policy` char(1) DEFAULT NULL COMMENT '是否有历史保单：0-否，1-是',
    `policy_expire_time` date DEFAULT NULL COMMENT '保单到期日期',
    `is_bid` int(11) DEFAULT NULL COMMENT '是否需要投标：0-否，1-是',
    `bid_result` int(11) DEFAULT NULL COMMENT '投标结果 0-失败 1-成功',
    `bid_start_date` date DEFAULT NULL COMMENT '投标开始时间',
    `bid_end_date` date DEFAULT NULL COMMENT '投标结束时间',
    `premium_budget` int(11) DEFAULT NULL COMMENT '保费预算',
    `contacter` varchar(50) DEFAULT NULL COMMENT '企业对接人',
    `contacter_post` varchar(50) DEFAULT NULL COMMENT '企业对接人职务',
    `add_health_service` char(1) DEFAULT NULL COMMENT '是否添加健康服务产品',
    `health_service_code` varchar(50) DEFAULT NULL COMMENT '健康服务产品编码',
    `health_service_name` varchar(50) DEFAULT NULL COMMENT '健康服务产品名称',
    `add_rescue_service` char(1) DEFAULT NULL COMMENT '是否添加救援服务产品',
    `rescue_service_code` varchar(50) DEFAULT NULL COMMENT '救援服务产品编码',
    `rescue_service_name` varchar(50) DEFAULT NULL COMMENT '救援服务产品名称',
    `employee_insurance_type` varchar(50) DEFAULT NULL COMMENT '员福险种类型, 存储员福险种类型编码，用逗号分隔',
    `general_insurance_type` varchar(100) DEFAULT NULL COMMENT '综合险种类型，存储综合险种名称，多个用逗号分隔',
    `remark` varchar(300) DEFAULT NULL COMMENT '备注',
    `team_time` datetime DEFAULT NULL COMMENT '组队完成时间',
    `submit_time` datetime DEFAULT NULL COMMENT '机会提交时间',
    `create_id` varchar(50) DEFAULT NULL COMMENT '创建人 当前用户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id` varchar(50) DEFAULT NULL COMMENT '更新人 默认为当前时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` int(11) DEFAULT NULL COMMENT '是否删除',
    `tenant_id` varchar(10) DEFAULT NULL COMMENT '租户ID',
    `log_time` datetime DEFAULT NULL COMMENT '项目日志最新更新时间',
    `kyc_report_time` datetime DEFAULT NULL COMMENT 'KYC报告生成时间',
    `risk_report_time` datetime DEFAULT NULL COMMENT '咨询风险报告生成时间',
    `coordination_accept_time` datetime DEFAULT NULL COMMENT '统筹领取时间',
    `assign_coordination_time` datetime DEFAULT NULL COMMENT '指派统筹时间',
    `assign_project_manager_time` datetime DEFAULT NULL COMMENT '指派项目经理时间',
    `project_team_info` text DEFAULT NULL COMMENT '项目人员/分工/比例信息（JSON格式）',
    `close_time` datetime DEFAULT NULL COMMENT '机会关闭时间',
    `summary_time` datetime DEFAULT NULL COMMENT '项目总结时间',
    `ranking_time` datetime DEFAULT NULL COMMENT '排分时间',
    `policy_time` datetime DEFAULT NULL COMMENT '出单时间',
    `suspend_time` datetime DEFAULT NULL COMMENT '暂停时间',
    `restart_time` datetime DEFAULT NULL COMMENT '重启时间',
    PRIMARY KEY (`id`),
    KEY `idx_opportunity_id` (`opportunity_id`),
    KEY `t_opportunity_detail_log_time_IDX` (`log_time`) USING BTREE,
    KEY `t_opportunity_detail_submit_time_IDX` (`submit_time`) USING BTREE,
    KEY `t_opportunity_detail_team_time_IDX` (`team_time`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机会明细表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_order 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_order` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `opportunity_id` varchar(16) DEFAULT NULL COMMENT '机会id',
    `tenant_id` varchar(16) DEFAULT NULL COMMENT '租户id',
    `company_code` varchar(16) DEFAULT NULL COMMENT '保险公司编码',
    `order_code` varchar(16) DEFAULT NULL COMMENT '订单编码',
    `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
    `policy_no` varchar(16) DEFAULT NULL COMMENT '保单号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `t_opportunity_order_tenant_id_IDX` (`tenant_id`,`opportunity_id`,`is_deleted`) USING BTREE,
    KEY `t_opportunity_order_opportunity_id_IDX` (`opportunity_id`) USING BTREE,
    KEY `t_opportunity_order_policy_no_IDX` (`policy_no`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_process 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_process` (
                                                       `Id` int(11) NOT NULL COMMENT '编号',
    `tenant_id` varchar(50) DEFAULT NULL COMMENT '租户id',
    `process_key` varchar(50) DEFAULT NULL COMMENT '流程定义key',
    `process_name` varchar(50) DEFAULT NULL COMMENT '流程名称',
    `opportunity_id` int(100) DEFAULT NULL COMMENT '机会id',
    `bpm_process_id` varchar(200) DEFAULT NULL COMMENT '业务流程表Id',
    `process_status` varchar(50) DEFAULT NULL COMMENT '流程状态',
    `create_id` varchar(50) DEFAULT NULL COMMENT '创建人编号 当前用户ID',
    `create_time` date DEFAULT NULL COMMENT '创建日期',
    `update_id` varchar(50) DEFAULT NULL COMMENT '更新人 默认为当前时间',
    `update_time` date DEFAULT NULL COMMENT '更新时间',
    `is_deleted` int(11) DEFAULT NULL COMMENT '删除',
    PRIMARY KEY (`Id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=latin1 ROW_FORMAT=DYNAMIC COMMENT='流程定义表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_process_log 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_process_log` (
                                                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `opportunity_id` int(11) DEFAULT NULL COMMENT '机会id',
    `tenant_id` varchar(16) DEFAULT NULL COMMENT '租户id',
    `process_id` varchar(16) DEFAULT NULL COMMENT '流程id',
    `operator_id` varchar(16) DEFAULT NULL COMMENT '操作人id',
    `target_id` varchar(200) DEFAULT NULL COMMENT '接收人',
    `operator_desc` varchar(200) DEFAULT NULL COMMENT '操作描述',
    `reason_type` int(11) DEFAULT NULL COMMENT '关闭原因',
    `resaon_desc` varchar(200) DEFAULT NULL COMMENT '原因描述',
    `is_node_complete_log` int(11) DEFAULT NULL COMMENT '是否节点完成日志 0 否 1 是',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `index_role_index` (`opportunity_id`,`tenant_id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='机会流程日志';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_team 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_team` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `opportunity_id` int(11) DEFAULT NULL COMMENT '机会id',
    `tenant_id` varchar(20) DEFAULT NULL,
    `user_id` varchar(16) DEFAULT NULL COMMENT '用户id',
    `join_type` int(11) DEFAULT NULL COMMENT '参与状态 0 待确认，1已确认  5 已拒绝',
    `times` int(11) DEFAULT NULL COMMENT '邀请次数',
    `create_id` varchar(50) NOT NULL COMMENT '创建人编号 当前用户ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建人姓名',
    `update_id` varchar(50) DEFAULT NULL,
    `update_time` datetime DEFAULT NULL COMMENT '创建日期 默认为当前时间',
    `is_deleted` tinyint(1) DEFAULT NULL COMMENT '是否已删除 0-否 1-是',
    `role_type` int(11) DEFAULT NULL COMMENT '角色ID',
    `is_default` tinyint(1) DEFAULT NULL COMMENT '是否默认成员 0-否 1-是',
    PRIMARY KEY (`id`),
    KEY `opportunity_id` (`opportunity_id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COMMENT='机会项目成员列表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_opportunity_team_division 结构
CREATE TABLE IF NOT EXISTS `t_opportunity_team_division` (
                                                             `Id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `opportunity_id` int(11) DEFAULT NULL COMMENT '机会id',
    `tenant_id` varchar(20) DEFAULT NULL,
    `user_id` varchar(16) DEFAULT NULL COMMENT '用户id',
    `division_id` varchar(64) DEFAULT NULL COMMENT '项目分工',
    `division_ratio` decimal(12,2) DEFAULT NULL COMMENT '分工比例',
    `status` int(11) DEFAULT NULL COMMENT '参与状态 0 待确认，1已确认  5 已拒绝',
    `create_id` varchar(50) DEFAULT NULL COMMENT '创建人编号 当前用户ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建人姓名',
    `update_id` varchar(50) DEFAULT NULL COMMENT '更新人id',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint(1) DEFAULT NULL COMMENT '删除',
    `num` int(11) DEFAULT NULL COMMENT '分工次数',
    `remark` varchar(250) DEFAULT NULL COMMENT '说明',
    `is_default` tinyint(1) DEFAULT NULL COMMENT '是否默认成员 0-否 1-是',
    PRIMARY KEY (`Id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='项目分工比例';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_process_define 结构
CREATE TABLE IF NOT EXISTS `t_process_define` (
                                                  `Id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `tenant_id` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '租户id',
    `process_key` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '流程定义key',
    `process_name` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '流程名称',
    `process_status` int(5) DEFAULT NULL COMMENT '流程状态 0 草稿  1 启用 2 停用',
    `process_desc` varchar(200) CHARACTER SET utf8 DEFAULT NULL COMMENT '流程描述',
    `process_condition` int(5) DEFAULT NULL COMMENT '流程条件 1 完成企业进阶信息录入 2 完成风险评估报告 3 完成投保信息录入',
    `process_type` int(5) DEFAULT NULL COMMENT '流程类型 1 默认  2自定义',
    `company_addrs` varchar(200) CHARACTER SET utf8 DEFAULT NULL COMMENT '公司所在地 多个地址，号隔开',
    `company_industry` varchar(200) CHARACTER SET utf8 DEFAULT NULL COMMENT '公司行业 多个行业，号隔开',
    `company_type` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '企业规模 A,B,C,D',
    `agent_legal_code` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '代理人法人公司',
    `agent_trading_center_code` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '营业部编码',
    `create_id` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人编号 当前用户ID',
    `create_time` date DEFAULT NULL COMMENT '创建日期',
    `update_id` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人 默认为当前时间',
    `update_time` date DEFAULT NULL COMMENT '更新时间',
    `is_deleted` int(11) DEFAULT NULL COMMENT '删除',
    PRIMARY KEY (`Id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=latin1 COMMENT='流程定义表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_role 结构
CREATE TABLE IF NOT EXISTS `t_role` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `role_name` varchar(50) DEFAULT NULL COMMENT '角色名称',
    `tenant_id` varchar(16) DEFAULT NULL COMMENT '归属租户Id',
    `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
    `sort` int(11) DEFAULT NULL COMMENT '排序',
    `role_type` int(11) DEFAULT NULL COMMENT '角色类型：1分公司统筹、2总公司统筹、3分公司项目经理、4总公司项目经理、99其他',
    `role_desc` varchar(100) DEFAULT NULL COMMENT '角色说明',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `index_tenant_id` (`tenant_id`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8 COMMENT='角色表';

-- 数据导出被取消选择。

-- 导出  表 kbc_elms_sta.t_role_auth 结构
CREATE TABLE IF NOT EXISTS `t_role_auth` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
    `role_id` int(11) NOT NULL COMMENT '编号',
    `role_name` varchar(50) DEFAULT NULL COMMENT '角色名称',
    `tenant_id` varchar(16) DEFAULT NULL COMMENT '归属租户Id',
    `auth_code` varchar(64) DEFAULT NULL COMMENT '功能ID',
    `auth_path` varchar(100) DEFAULT NULL COMMENT '权限位路径 1/2/3',
    `create_id` varchar(16) DEFAULT NULL COMMENT '创建人编号',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_id` varchar(16) DEFAULT NULL COMMENT '更新人编号',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` int(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除  1已删除',
    `sort`
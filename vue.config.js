module.exports = {
  publicPath: "/kbc-elms",
  lintOnSave: false,
  devServer: {
    open: process.platform === "darwin",
    host: "0.0.0.0", // 允许外部ip访问
    port: 8022,     // 端口
    https: false,  // 启用https
    hot: true,
    overlay: {
      warnings: false,
      errors: false
    }, // 错误、警告在页面弹出
    proxy: {
      "/mock": {
        //target:"https://kbc-sta.kbao123.com/gateway/kbc-elms",
        target:"http://localhost:7003",
        changeOrigin: true, // 允许websockets跨域
        // ws: true,
        pathRewrite: {
          "^/mock": ""
        }
      }
    } // 代理转发配置，用于调试环境
  },
  configureWebpack: config => {
    config.optimization = {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vue: {
            name: 'vue',
            test: /[\\/]node_modules[\\/]vue[\\/]/,
            priority: -10
          },
          vuex: {
            name: 'vuex',
            test: /[\\/]node_modules[\\/]vuex[\\/]/,
            priority: -10
          },
          'vue-router': {
            name: 'vue-router',
            test: /[\\/]node_modules[\\/]vue-router[\\/]/,
            priority: -10
          },
          'element-ui': {
            name: 'element-ui',
            test: /[\\/]node_modules[\\/]element-ui[\\/]/,
            priority: -10
          },
          'lodash':{
            name: 'lodash',
            test: /[\\/]node_modules[\\/]lodash[\\/]/,
            priority: -10
          },
          'lodash-es': {
            name: 'lodash-es',
            test: /[\\/]node_modules[\\/]lodash-es[\\/]/,
            priority: -10
          },
          'element-ui': {
            name: 'element-ui',
            test: /[\\/]node_modules[\\/]element-ui[\\/]/,
            priority: -10
          },
          'quill': {
            name: 'quill',
            test: /[\\/]node_modules[\\/]quill[\\/]/,
            priority: -10
          },
          'vue-quill-editor': {
            name: 'vue-quill-editor',
            test: /[\\/]node_modules[\\/]vue-quill-editor[\\/]/,
            priority: -10
          },
          'vuedraggable': {
            name: 'vuedraggable',
            test: /[\\/]node_modules[\\/]vuedraggable[\\/]/,
            priority: -10
          },
          'vendors': {
            name: 'vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: -20
          },

        }
      }
    }
  }
};
